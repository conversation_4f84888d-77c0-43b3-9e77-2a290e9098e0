apiVersion: apps/v1
kind: Deployment
metadata:
  name: conversas-link-front
  namespace: conversas-link
spec:
  replicas: 1
  selector:
    matchLabels:
      app: conversas-link-front
  template:
    metadata:
      labels:
        app: conversas-link-front
    spec:
      imagePullSecrets:
        - name: gitlab-registry-secret
      containers:
        - name: front
          image: registry.gitlab.com/plataformazw/ia/conversa-link/front:main
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          envFrom:
            - configMapRef:
                name: conversas-link-front-config
          resources:
            requests:
              cpu: "50m"
              memory: "128Mi"    
            limits:
              cpu: "200m"     
              memory: "256Gi"
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 3

          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 2
            periodSeconds: 5
            timeoutSeconds: 2
            failureThreshold: 2