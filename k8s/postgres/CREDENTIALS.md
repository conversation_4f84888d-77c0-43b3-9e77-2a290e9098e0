# 🔐 Credenciais Seguras do PostgreSQL

## ⚠️ IMPORTANTE
**Este arquivo contém senhas sensíveis. NÃO commite este arquivo no Git!**
**Mantenha estas credenciais em local seguro.**

---

## 🗄️ PostgreSQL - Usuário Administrador
```
Usuário: postgres
Senha: XtzDbODFDqPY9hESxeCvUZscxoNvuFjUIzwHsSMxrak=
```

## 👤 PostgreSQL - Usuário da Aplicação
```
Usuário: conversas_ai_user
Senha: 2NzjOT4W8wI6ghKSS8BBHK8IwtGQ+EHwo+NOoRE+gX0=
Database: conversas_ai
```

## 🌐 URL de Conexão Completa
```
**************************************************************************************************************************/conversas_ai
```

## 🌐 Túnel Cloudflare Zero Trust
```
URL: https://pg.conversas.ai
Proteção: Cloudflare Access (apenas emails autorizados)
Porta: 5432 (padrão PostgreSQL)
```

---

## 🔒 Características das Senhas

### ✅ Senhas Seguras Geradas
- **Comprimento**: 44 caracteres cada
- **Método**: OpenSSL random base64
- **Entropia**: 256 bits
- **Caracteres**: A-Z, a-z, 0-9, +, /, =

### 🛡️ Segurança Implementada
- **Senhas aleatórias** geradas criptograficamente
- **Base64 encoding** para compatibilidade
- **Diferentes senhas** para cada serviço
- **Armazenamento seguro** em Kubernetes Secrets

---

## 🔄 Rotação de Senhas

### Para alterar as senhas:

1. **Gerar novas senhas**:
```bash
# Gerar nova senha
openssl rand -base64 32

# Codificar para Secret
echo -n "NOVA_SENHA" | base64
```

2. **Atualizar Secrets**:
```bash
kubectl patch secret postgres-secrets -n postgres -p='{"data":{"POSTGRES_PASSWORD":"NOVA_SENHA_BASE64"}}'
```

3. **Reiniciar pods**:
```bash
kubectl rollout restart deployment/postgres -n postgres
kubectl rollout restart deployment/pgadmin -n postgres
```

---

## 📋 Checklist de Segurança

- ✅ Senhas com alta entropia (256 bits)
- ✅ Senhas únicas para cada serviço
- ✅ Armazenamento em Kubernetes Secrets
- ✅ Não exposição em logs ou configurações
- ✅ Acesso restrito ao namespace
- ⚠️ **TODO**: Implementar rotação automática
- ⚠️ **TODO**: Configurar backup criptografado
- ⚠️ **TODO**: Implementar auditoria de acesso

---

## 🚨 Em Caso de Comprometimento

1. **Imediatamente**:
   - Gerar novas senhas
   - Atualizar todos os Secrets
   - Reiniciar todos os pods

2. **Investigar**:
   - Verificar logs de acesso
   - Identificar possível vazamento
   - Revisar permissões

3. **Prevenir**:
   - Implementar rotação automática
   - Melhorar monitoramento
   - Revisar políticas de acesso

---

## 📞 Contatos de Emergência

Em caso de problemas de segurança:
1. **Administrador do Sistema**: [contato]
2. **Equipe de Segurança**: [contato]
3. **Responsável pela Aplicação**: [contato]

---

**⚠️ LEMBRE-SE: Mantenha este arquivo seguro e não o compartilhe desnecessariamente!**
