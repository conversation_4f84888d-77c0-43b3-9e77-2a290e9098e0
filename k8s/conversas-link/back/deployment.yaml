apiVersion: apps/v1
kind: Deployment
metadata:
  name: conversas-link-back
  namespace: conversas-link
spec:
  replicas: 1
  selector:
    matchLabels:
      app: conversas-link-back
  template:
    metadata:
      labels:
        app: conversas-link-back
    spec:
      imagePullSecrets:
        - name: gitlab-registry-secret
      containers:
        - name: back
          image: registry.gitlab.com/plataformazw/ia/conversa-link/api:main
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: conversas-link-back-config
            - secretRef:
                name: conversas-link-api-secrets
          resources:
            requests:
              cpu: "300m"
              memory: "512Mi"    
            limits:
              cpu: "800m"
              memory: "1Gi"
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3

          readinessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 2
