apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: conversas-link-front-ingress
  namespace: conversas-link
  annotations:
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: conversaslink-auth
    nginx.ingress.kubernetes.io/auth-realm: "Conversa Link Authentication"
spec:
  ingressClassName: nginx
  rules:
    - host: link.conversas.ai
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: conversas-link-front
                port:
                  number: 80