apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jaeger-ingress
  namespace: jaeger
  annotations:
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: jaeger-auth
    nginx.ingress.kubernetes.io/auth-realm: "Redis Insight Authentication"
spec:
  ingressClassName: nginx
  rules:
  - host: jaeger.conversas.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: jaeger-service
            port:
              number: 16686