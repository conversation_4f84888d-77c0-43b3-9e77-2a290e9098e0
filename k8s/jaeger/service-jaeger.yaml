apiVersion: v1
kind: Service
metadata:
  name: jaeger-service
  namespace: jaeger
  labels:
    app: jaeger
spec:
  selector:
    app: jaeger
  ports:
  - port: 5775
    targetPort: 5775
    protocol: UDP
    name: udp-5775
  - port: 6831
    targetPort: 6831
    protocol: UDP
    name: udp-6831
  - port: 6832
    targetPort: 6832
    protocol: UDP
    name: udp-6832
  - port: 5778
    targetPort: 5778
    name: tcp-5778
  - port: 16686
    targetPort: 16686
    name: tcp-16686
  type: ClusterIP
