#!/bin/bash

# Script para testar diferentes métodos de autenticação no Grafana
# Uso: ./testar-autenticacao.sh

set -e

GRAFANA_URL="https://grafana.conversas.ai"
GRAFANA_TOKEN="glsa_DZUZLg8CGbqq8rQ5eGwF1IYit8BlxafG_63273e3b"
GRAFANA_USER="admin"
GRAFANA_PASS="Oogf@r7&E715"

echo "🔐 Testando métodos de autenticação no Grafana..."
echo "================================================="
echo ""

# Função para testar autenticação
test_auth() {
    local method=$1
    local auth_header=$2
    local description=$3
    
    echo "🔄 Testando: $description"
    
    response=$(curl -s -w "\n%{http_code}" \
        -H "$auth_header" \
        -H "Content-Type: application/json" \
        "$GRAFANA_URL/api/user" 2>/dev/null || echo -e "\nERROR")
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [[ "$http_code" == "ERROR" ]]; then
        echo "   ❌ Erro de conexão"
        return 1
    elif [[ $http_code -eq 200 ]]; then
        echo "   ✅ Sucesso! (HTTP $http_code)"
        user_info=$(echo "$body" | jq -r '.login // .email // "N/A"' 2>/dev/null || echo "N/A")
        echo "   👤 Usuário: $user_info"
        return 0
    else
        echo "   ❌ Falha (HTTP $http_code)"
        echo "   📝 Response: $body"
        return 1
    fi
}

echo "1️⃣ Testando Service Account Token (Bearer)..."
if test_auth "bearer" "Authorization: Bearer $GRAFANA_TOKEN" "Service Account Token"; then
    AUTH_METHOD="bearer"
    AUTH_HEADER="Authorization: Bearer $GRAFANA_TOKEN"
    echo "   🎉 Service Account Token funcionando!"
else
    echo "   ❌ Service Account Token não funciona"
fi
echo ""

echo "2️⃣ Testando Basic Auth (admin/senha)..."
if test_auth "basic" "Authorization: Basic $(echo -n "$GRAFANA_USER:$GRAFANA_PASS" | base64)" "Basic Auth"; then
    AUTH_METHOD="basic"
    AUTH_HEADER="Authorization: Basic $(echo -n "$GRAFANA_USER:$GRAFANA_PASS" | base64)"
    echo "   🎉 Basic Auth funcionando!"
else
    echo "   ❌ Basic Auth não funciona"
fi
echo ""

echo "3️⃣ Testando API Key (formato alternativo)..."
if test_auth "apikey" "Authorization: Bearer $GRAFANA_TOKEN" "API Key Bearer"; then
    AUTH_METHOD="apikey"
    AUTH_HEADER="Authorization: Bearer $GRAFANA_TOKEN"
    echo "   🎉 API Key funcionando!"
else
    echo "   ❌ API Key não funciona"
fi
echo ""

# Verificar se algum método funcionou
if [[ -n "${AUTH_METHOD:-}" ]]; then
    echo "✅ Método de autenticação encontrado: $AUTH_METHOD"
    echo "🔧 Header de autenticação: $AUTH_HEADER"
    echo ""
    
    # Testar alguns endpoints importantes
    echo "🧪 Testando endpoints importantes..."
    
    # Função para testar endpoint
    test_endpoint() {
        local endpoint=$1
        local description=$2
        
        response=$(curl -s -w "\n%{http_code}" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            "$GRAFANA_URL/api/$endpoint" 2>/dev/null || echo -e "\nERROR")
        
        http_code=$(echo "$response" | tail -n1)
        
        if [[ $http_code -eq 200 ]]; then
            echo "   ✅ $description"
        elif [[ $http_code -eq 404 ]]; then
            echo "   ❌ $description (não encontrado)"
        else
            echo "   ⚠️  $description (HTTP $http_code)"
        fi
    }
    
    test_endpoint "dashboards/uid/waitress-queue-monitoring" "Dashboard Waitress"
    test_endpoint "alertmanager/grafana/config" "Alertmanager Config"
    test_endpoint "ruler/grafana/api/v1/rules" "Alert Rules"
    test_endpoint "v1/provisioning/contact-points" "Contact Points (Provisioning)"
    test_endpoint "v1/provisioning/alert-rules" "Alert Rules (Provisioning)"
    
    echo ""
    echo "💾 Salvando configuração de autenticação..."
    
    # Criar arquivo de configuração
    cat > auth-config.sh << EOF
#!/bin/bash
# Configuração de autenticação para scripts do Grafana
# Gerado automaticamente em $(date)

GRAFANA_URL="$GRAFANA_URL"
AUTH_METHOD="$AUTH_METHOD"
AUTH_HEADER="$AUTH_HEADER"

# Função para fazer chamadas autenticadas
grafana_auth_call() {
    local method=\$1
    local endpoint=\$2
    local data=\$3
    
    if [[ -n "\$data" ]]; then
        curl -s -X "\$method" \\
            -H "\$AUTH_HEADER" \\
            -H "Content-Type: application/json" \\
            -d "\$data" \\
            "\$GRAFANA_URL/api/\$endpoint"
    else
        curl -s -X "\$method" \\
            -H "\$AUTH_HEADER" \\
            -H "Content-Type: application/json" \\
            "\$GRAFANA_URL/api/\$endpoint"
    fi
}
EOF
    
    chmod +x auth-config.sh
    echo "   📁 Arquivo criado: auth-config.sh"
    echo ""
    
    echo "🚀 Próximos passos:"
    echo "   1. Use: source auth-config.sh"
    echo "   2. Execute: ./criar-alertas-com-auth.sh"
    echo "   3. Ou configure manualmente: CRIAR-ALERTAS-PASSO-A-PASSO.md"
    
else
    echo "❌ Nenhum método de autenticação funcionou!"
    echo ""
    echo "🔧 Possíveis soluções:"
    echo "   1. Verificar se o service account token está correto"
    echo "   2. Verificar se o token não expirou"
    echo "   3. Verificar se as credenciais admin estão corretas"
    echo "   4. Usar configuração manual: CRIAR-ALERTAS-PASSO-A-PASSO.md"
    echo ""
    echo "📞 Para obter um novo token:"
    echo "   1. Acesse: https://grafana.conversas.ai/org/serviceaccounts"
    echo "   2. Crie um novo service account com permissões de Admin"
    echo "   3. Gere um novo token"
fi

echo ""
echo "📋 Informações de debug:"
echo "   URL: $GRAFANA_URL"
echo "   Token: ${GRAFANA_TOKEN:0:20}..."
echo "   User: $GRAFANA_USER"
echo "   Pass: ${GRAFANA_PASS:0:3}..."
