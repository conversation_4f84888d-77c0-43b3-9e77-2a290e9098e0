#!/bin/bash

# Script atualizado para verificar alertas usando endpoints corretos
# Uso: ./verificar-alertas-v2.sh

set -e

# Carregar configuração de autenticação
if [[ -f "auth-config.sh" ]]; then
    source auth-config.sh
    echo "✅ Configuração de autenticação carregada ($AUTH_METHOD)"
else
    echo "❌ Execute ./testar-autenticacao.sh primeiro"
    exit 1
fi

echo ""
echo "🔍 Verificando configuração dos alertas (v2)..."
echo "==============================================="
echo ""

# Função para fazer chamadas autenticadas
check_endpoint() {
    local endpoint=$1
    local description=$2
    local search_term=$3
    
    echo "🔄 $description"
    
    response=$(curl -s -w "\n%{http_code}" \
        -H "$AUTH_HEADER" \
        -H "Content-Type: application/json" \
        "$GRAFANA_URL/api/$endpoint" 2>/dev/null || echo -e "\nERROR")
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [[ "$http_code" == "ERROR" ]]; then
        echo "   ❌ Erro de conexão"
        return 1
    elif [[ $http_code -eq 200 ]]; then
        if [[ -n "$search_term" ]]; then
            if echo "$body" | grep -q "$search_term"; then
                echo "   ✅ Encontrado!"
                return 0
            else
                echo "   ❌ Não encontrado (endpoint OK, mas item não existe)"
                return 1
            fi
        else
            echo "   ✅ Endpoint funcionando"
            return 0
        fi
    else
        echo "   ❌ Erro HTTP $http_code"
        return 1
    fi
}

# 1. Verificar Contact Points
echo "📱 1. Verificando Contact Points..."
if check_endpoint "v1/provisioning/contact-points" "Contact Points (Provisioning)" "google-chat-conversas-ai"; then
    echo "   🎉 Contact Point 'google-chat-conversas-ai' encontrado!"
else
    echo "   ❌ Contact Point não encontrado"
fi
echo ""

# 2. Verificar Alert Rules
echo "⚠️  2. Verificando Alert Rules..."
if check_endpoint "v1/provisioning/alert-rules" "Alert Rules (Provisioning)" "waitress"; then
    echo "   🎉 Alert Rule para Waitress encontrada!"
else
    echo "   ❌ Alert Rule não encontrada"
fi
echo ""

# 3. Verificar Dashboard
echo "📊 3. Verificando Dashboard..."
if check_endpoint "dashboards/uid/waitress-queue-monitoring" "Dashboard Waitress" ""; then
    echo "   🎉 Dashboard encontrado!"
    echo "   🔗 URL: $GRAFANA_URL/d/waitress-queue-monitoring"
else
    echo "   ❌ Dashboard não encontrado"
fi
echo ""

# 4. Verificar Data Source Loki
echo "🔍 4. Verificando Data Source Loki..."
if check_endpoint "datasources" "Data Sources" "loki"; then
    echo "   🎉 Data Source Loki encontrado!"
else
    echo "   ❌ Data Source Loki não encontrado"
fi
echo ""

# 5. Testar Query no Loki (se disponível)
echo "🔍 5. Testando Query no Loki..."
loki_response=$(curl -s -H "$AUTH_HEADER" \
    "$GRAFANA_URL/api/datasources/proxy/uid/cen20vehcfpq8b/loki/api/v1/query?query=%7Bapp%3D%22conversas-ai-api%22%7D%20%7C%3D%20%22WARNING%3Awaitress.queue%3ATask%22&limit=1" 2>/dev/null || echo "{}")

if echo "$loki_response" | grep -q "data"; then
    echo "   ✅ Query Loki funcionando"
    
    # Verificar se há dados
    if echo "$loki_response" | grep -q '"result":\['; then
        result_count=$(echo "$loki_response" | grep -o '"result":\[[^]]*\]' | grep -o ',' | wc -l || echo "0")
        if [[ $result_count -gt 0 ]]; then
            echo "   ⚠️  Há warnings nos logs atualmente"
        else
            echo "   ℹ️  Nenhum warning encontrado (normal se não há problemas)"
        fi
    fi
else
    echo "   ❌ Erro ao executar query no Loki"
fi
echo ""

# 6. Testar Webhook
echo "🧪 6. Testando Webhook do Google Chat..."
if ./test-google-chat-webhook.sh >/dev/null 2>&1; then
    echo "   ✅ Webhook funcionando"
else
    echo "   ❌ Problema com webhook"
fi
echo ""

# 7. Verificar status dos alertas ativos
echo "🚨 7. Verificando alertas ativos..."
alerts_response=$(curl -s -H "$AUTH_HEADER" \
    "$GRAFANA_URL/api/alertmanager/grafana/api/v1/alerts" 2>/dev/null || echo "[]")

if echo "$alerts_response" | grep -q "waitress"; then
    echo "   🔥 Há alertas ativos relacionados ao waitress!"
else
    echo "   ✅ Nenhum alerta ativo (isso é bom)"
fi
echo ""

# 8. Listar todos os contact points criados
echo "📋 8. Listando Contact Points criados..."
contacts_response=$(curl -s -H "$AUTH_HEADER" \
    "$GRAFANA_URL/api/v1/provisioning/contact-points" 2>/dev/null || echo "[]")

contact_count=$(echo "$contacts_response" | grep -o '"name"' | wc -l || echo "0")
echo "   📊 Total de contact points: $contact_count"

if echo "$contacts_response" | grep -q "google-chat"; then
    echo "   ✅ Contact point do Google Chat encontrado"
else
    echo "   ❌ Contact point do Google Chat não encontrado"
fi
echo ""

# 9. Listar todas as alert rules criadas
echo "📋 9. Listando Alert Rules criadas..."
rules_response=$(curl -s -H "$AUTH_HEADER" \
    "$GRAFANA_URL/api/v1/provisioning/alert-rules" 2>/dev/null || echo "[]")

rules_count=$(echo "$rules_response" | grep -o '"title"' | wc -l || echo "0")
echo "   📊 Total de alert rules: $rules_count"

if echo "$rules_response" | grep -q "Waitress"; then
    echo "   ✅ Alert rule do Waitress encontrada"
    
    # Extrair detalhes da regra
    rule_uid=$(echo "$rules_response" | grep -o '"uid":"[^"]*waitress[^"]*"' | head -1 | cut -d'"' -f4 || echo "N/A")
    echo "   🆔 UID da regra: $rule_uid"
else
    echo "   ❌ Alert rule do Waitress não encontrada"
fi
echo ""

# Resumo Final
echo "📋 RESUMO FINAL"
echo "==============="
echo ""

# Contar itens OK
ok_count=0
total_count=6

# Contact Point
if echo "$contacts_response" | grep -q "google-chat"; then
    echo "✅ Contact Point: OK"
    ((ok_count++))
else
    echo "❌ Contact Point: FALTANDO"
fi

# Alert Rule
if echo "$rules_response" | grep -q "Waitress"; then
    echo "✅ Alert Rule: OK"
    ((ok_count++))
else
    echo "❌ Alert Rule: FALTANDO"
fi

# Dashboard
dashboard_check=$(curl -s -H "$AUTH_HEADER" "$GRAFANA_URL/api/dashboards/uid/waitress-queue-monitoring" 2>/dev/null || echo "{}")
if echo "$dashboard_check" | grep -q "waitress"; then
    echo "✅ Dashboard: OK"
    ((ok_count++))
else
    echo "❌ Dashboard: FALTANDO"
fi

# Data Source
datasource_check=$(curl -s -H "$AUTH_HEADER" "$GRAFANA_URL/api/datasources" 2>/dev/null || echo "[]")
if echo "$datasource_check" | grep -q "loki"; then
    echo "✅ Data Source Loki: OK"
    ((ok_count++))
else
    echo "❌ Data Source Loki: FALTANDO"
fi

# Query Loki
if echo "$loki_response" | grep -q "data"; then
    echo "✅ Query Loki: OK"
    ((ok_count++))
else
    echo "❌ Query Loki: PROBLEMA"
fi

# Webhook
if ./test-google-chat-webhook.sh >/dev/null 2>&1; then
    echo "✅ Webhook: OK"
    ((ok_count++))
else
    echo "❌ Webhook: PROBLEMA"
fi

echo ""
echo "🎯 Status Geral: $ok_count/$total_count itens OK"

if [[ $ok_count -eq $total_count ]]; then
    echo "🎉 CONFIGURAÇÃO COMPLETA E FUNCIONANDO!"
    echo ""
    echo "📈 Links importantes:"
    echo "   • Dashboard: $GRAFANA_URL/d/waitress-queue-monitoring"
    echo "   • Alertas: $GRAFANA_URL/alerting/list"
    echo "   • Contact Points: $GRAFANA_URL/alerting/notifications"
elif [[ $ok_count -ge 4 ]]; then
    echo "⚠️  CONFIGURAÇÃO QUASE COMPLETA ($ok_count/$total_count)"
    echo "   👉 Alguns itens podem precisar de ajustes manuais"
else
    echo "❌ CONFIGURAÇÃO INCOMPLETA ($ok_count/$total_count)"
    echo "   👉 Use configuração manual: CRIAR-ALERTAS-PASSO-A-PASSO.md"
fi

echo ""
echo "🔄 Para executar novamente: ./verificar-alertas-v2.sh"
