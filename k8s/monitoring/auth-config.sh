#!/bin/bash
# Configuração de autenticação para scripts do Grafana
# Gerado automaticamente em sex 13 jun 2025 15:52:11 -03

GRAFANA_URL="https://grafana.conversas.ai"
AUTH_METHOD="basic"
AUTH_HEADER="Authorization: Basic YWRtaW46T29nZkByNyZFNzE1"

# Função para fazer chamadas autenticadas
grafana_auth_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    if [[ -n "$data" ]]; then
        curl -s -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$GRAFANA_URL/api/$endpoint"
    else
        curl -s -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            "$GRAFANA_URL/api/$endpoint"
    fi
}
