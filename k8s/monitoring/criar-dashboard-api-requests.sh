#!/bin/bash

# Script para criar dashboard de monitoramento de requests da API Conversas AI
# Monitora requests HTTP para api.conversas.ai através dos logs do Loki
# Uso: ./criar-dashboard-api-requests.sh

set -e

# Carregar configuração de autenticação
if [[ -f "auth-config.sh" ]]; then
    source auth-config.sh
    echo "✅ Configuração de autenticação carregada"
else
    echo "❌ Execute ./testar-autenticacao.sh primeiro"
    exit 1
fi

echo ""
echo "📊 Criando Dashboard de Monitoramento de API Requests..."
echo "====================================================="
echo ""

# Função para fazer chamadas autenticadas
call_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo "🔄 $description"
    echo "   $method /api/$endpoint"
    
    if [[ -n "$data" ]]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$GRAFANA_URL/api/$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            "$GRAFANA_URL/api/$endpoint")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "   Status: HTTP $http_code"
    
    if [[ $http_code -ge 200 && $http_code -lt 300 ]]; then
        echo "   ✅ Sucesso!"
        return 0
    else
        echo "   ❌ Erro: $body"
        return 1
    fi
}

# 1. Verificar se o data source Loki está disponível
echo "🔍 Passo 1: Verificando Data Source Loki..."
if call_api "GET" "datasources" "" "Verificando data sources"; then
    echo "✅ Data sources verificados"
else
    echo "❌ Erro ao verificar data sources"
    exit 1
fi
echo ""

# 2. Importar Dashboard de API Requests
echo "📊 Passo 2: Importando Dashboard de API Requests..."

DASHBOARD_JSON=$(cat grafana-dashboards/api-requests-monitoring.json)
if call_api "POST" "dashboards/db" "$DASHBOARD_JSON" "Importando Dashboard API Requests"; then
    echo "✅ Dashboard de API Requests importado com sucesso!"
    dashboard_created=true
else
    echo "⚠️  Dashboard pode já existir, tentando atualizar..."
    
    # Tentar atualizar dashboard existente
    if call_api "POST" "dashboards/db" "$DASHBOARD_JSON" "Atualizando Dashboard API Requests"; then
        echo "✅ Dashboard de API Requests atualizado!"
        dashboard_created=true
    else
        echo "❌ Falha ao criar/atualizar dashboard"
        dashboard_created=false
    fi
fi
echo ""

# 3. Verificar se o dashboard foi criado
echo "🔍 Passo 3: Verificando Dashboard criado..."
if call_api "GET" "dashboards/uid/api-requests-conversas-ai" "" "Verificando dashboard"; then
    echo "✅ Dashboard acessível"
else
    echo "❌ Dashboard não encontrado"
fi
echo ""

# 4. Testar queries básicas no Loki
echo "🧪 Passo 4: Testando queries no Loki..."

# Testar query básica de logs da API
echo "   Testando query de logs da API..."
loki_test=$(curl -s -H "$AUTH_HEADER" \
    "$GRAFANA_URL/api/datasources/proxy/uid/cen20vehcfpq8b/loki/api/v1/query?query=%7Bapp%3D%22conversas-ai-api%22%7D&limit=1" 2>/dev/null || echo "{}")

if echo "$loki_test" | grep -q "data"; then
    echo "   ✅ Query básica funcionando"
    
    # Verificar se há logs HTTP
    http_logs_test=$(curl -s -H "$AUTH_HEADER" \
        "$GRAFANA_URL/api/datasources/proxy/uid/cen20vehcfpq8b/loki/api/v1/query?query=%7Bapp%3D%22conversas-ai-api%22%7D%20%7C~%20%22(GET%7CPOST%7CPUT%7CDELETE%7CPATCH)%22&limit=1" 2>/dev/null || echo "{}")
    
    if echo "$http_logs_test" | grep -q '"result":\['; then
        echo "   ✅ Logs HTTP encontrados"
    else
        echo "   ⚠️  Nenhum log HTTP encontrado (pode ser normal se não há tráfego)"
    fi
else
    echo "   ❌ Erro ao executar query no Loki"
fi
echo ""

echo "🎉 Dashboard de API Requests criado com sucesso!"
echo ""

if [[ "${dashboard_created:-false}" == "true" ]]; then
    echo "✅ Dashboard funcionando: api-requests-conversas-ai"
    echo ""
    echo "🔗 URLs importantes:"
    echo "   • Dashboard API Requests: https://grafana.conversas.ai/d/api-requests-conversas-ai"
    echo "   • Grafana Home: https://grafana.conversas.ai/"
    echo "   • Explore Loki: https://grafana.conversas.ai/explore"
    echo ""
    echo "📊 Métricas disponíveis:"
    echo "   ✅ Total de requests (últimos 5 minutos)"
    echo "   ✅ Requests por minuto"
    echo "   ✅ Distribuição de códigos HTTP"
    echo "   ✅ Distribuição de métodos HTTP"
    echo "   ✅ Taxa de erro (4xx + 5xx)"
    echo "   ✅ Top endpoints mais acessados"
    echo "   ✅ Tempo de resposta (P50, P95, P99)"
    echo "   ✅ Logs recentes de requests"
    echo ""
    echo "🧪 Para testar:"
    echo "   1. Acesse o dashboard para visualizar métricas"
    echo "   2. Faça algumas requisições para api.conversas.ai"
    echo "   3. Observe as métricas sendo atualizadas"
    echo ""
    echo "🔍 Queries de exemplo para Explore:"
    echo "   • Total requests: sum(count_over_time({app=\"conversas-ai-api\"} |~ \"(GET|POST|PUT|DELETE|PATCH)\" [5m]))"
    echo "   • Requests por minuto: sum(rate({app=\"conversas-ai-api\"} |~ \"(GET|POST|PUT|DELETE|PATCH)\" [1m]))"
    echo "   • Logs HTTP: {app=\"conversas-ai-api\"} |~ \"(GET|POST|PUT|DELETE|PATCH)\""
else
    echo "❌ Não foi possível criar o dashboard"
    echo ""
    echo "📋 Solução alternativa:"
    echo "   1. Acesse: https://grafana.conversas.ai/dashboard/import"
    echo "   2. Cole o conteúdo de: grafana-dashboards/api-requests-monitoring.json"
    echo "   3. Configure manualmente o dashboard"
fi

echo ""
echo "💡 Dicas:"
echo "   • O dashboard monitora logs da aplicação conversas-ai-api"
echo "   • As métricas são baseadas em padrões de logs HTTP"
echo "   • Para melhor precisão, configure logs estruturados na aplicação"
echo "   • Use o Explore do Grafana para testar e ajustar queries"

echo ""
echo "📈 Monitoramento ativo para api.conversas.ai!"
