# 🚨 Sistema de Alertas Waitress Queue - Conversas AI

Sistema completo de monitoramento e alertas para detectar problemas de performance no Waitress Queue da aplicação conversas-ai-api.

## 🎯 Objetivo

Monitorar logs da aplicação `conversas-ai-api` e alertar quando há mais de 5 warnings `WARNING:waitress.queue:Task` em 5 minutos, indicando possível sobrecarga do servidor.

## 📋 Componentes

### ✅ Dashboard
- **Nome**: Waitress Queue Monitoring - Conversas AI
- **URL**: https://grafana.conversas.ai/d/waitress-queue-monitoring
- **Função**: Visualização em tempo real dos warnings

### ✅ Contact Point
- **Nome**: Google Chat Conversas AI
- **Tipo**: Webhook para Google Chat
- **Função**: Enviar notificações de alerta

### ✅ Alert Rule
- **Nome**: Waitress Queue Task Warnings High
- **Query**: `sum(count_over_time({app="conversas-ai-api"} |= "WARNING:waitress.queue:Task" [5m]))`
- **Condição**: > 5 warnings em 5 minutos
- **Função**: Detectar problemas de performance

### ✅ Dashboard API Requests (Nginx Ingress)
- **Nome**: Nginx Ingress API Monitoring - Conversas AI
- **URL**: https://grafana.conversas.ai/d/nginx-ingress-api-conversas-ai
- **Função**: Monitoramento completo de requests HTTP via logs do Nginx Ingress
- **Métricas**: Requests/sec, tempos de resposta, códigos HTTP, endpoints mais acessados

## 🚀 Instalação Rápida

### 1. Configurar Autenticação
```bash
cd k8s/monitoring
./testar-autenticacao.sh
```

### 2. Criar Sistema Completo
```bash
./criar-alertas-waitress.sh
```

### 3. Testar Webhook
```bash
./test-google-chat-webhook.sh
```

### 4. Criar Dashboard de API Requests (Nginx Ingress)
```bash
./criar-dashboard-nginx-ingress.sh
```

### 5. Verificar Status
```bash
./verificar-alertas-v2.sh
```

## 📊 URLs Importantes

- **Dashboard Waitress Queue**: https://grafana.conversas.ai/d/waitress-queue-monitoring
- **Dashboard API Requests (Nginx Ingress)**: https://grafana.conversas.ai/d/nginx-ingress-api-conversas-ai
- **Lista de Alertas**: https://grafana.conversas.ai/alerting/list
- **Contact Points**: https://grafana.conversas.ai/alerting/notifications
- **Grafana Home**: https://grafana.conversas.ai/

## 🔧 Scripts Disponíveis

### Scripts Principais
- `criar-alertas-waitress.sh` - Cria dashboard, contact point e alert rule
- `criar-dashboard-nginx-ingress.sh` - Cria dashboard de monitoramento da API via Nginx Ingress
- `testar-autenticacao.sh` - Testa autenticação com Grafana
- `test-google-chat-webhook.sh` - Testa webhook do Google Chat
- `verificar-alertas-v2.sh` - Verifica status completo do sistema

### Arquivos de Configuração
- `auth-config.sh` - Configuração de autenticação (gerado automaticamente)
- `grafana-dashboards/waitress-queue-monitoring.json` - Dashboard JSON
- `grafana-alerts/` - Configurações de alertas (referência)

## 🧪 Como Testar

### 1. Verificar Dashboard
1. Acesse: https://grafana.conversas.ai/d/waitress-queue-monitoring
2. Verifique se os painéis estão carregando dados
3. Observe o gráfico de warnings ao longo do tempo

### 2. Testar Query no Explore
1. Vá para **Explore** no Grafana
2. Selecione data source **Loki**
3. Execute:
   ```
   sum(count_over_time({app="conversas-ai-api"} |= "WARNING:waitress.queue:Task" [5m]))
   ```

### 3. Verificar Logs Atuais
```bash
kubectl logs -n conversas-ai deployment/conversas-ai-api | grep "WARNING:waitress.queue:Task"
```

### 4. Simular Alerta (Opcional)
Para testar se o alerta funciona, você pode gerar logs artificiais ou aguardar situações reais de sobrecarga.

## 🚨 Como Funciona o Alerta

### Fluxo de Monitoramento
```
Aplicação conversas-ai-api → Logs → Loki → Query → Condição → Google Chat
```

### Condições de Disparo
1. **Aplicação gera** logs com `WARNING:waitress.queue:Task`
2. **Loki coleta** os logs automaticamente
3. **Query conta** quantos warnings nos últimos 5 minutos
4. **Se > 5 warnings**, dispara alerta
5. **Google Chat recebe** notificação

### Mensagem de Alerta
```
🚨 Alerta Conversas AI - Waitress Queue

ALERTA CRÍTICO: Waitress Queue Warnings Alto

Descrição: Detectados mais de 5 warnings WARNING:waitress.queue:Task 
nos últimos 5 minutos na aplicação conversas-ai-api.

Serviço: conversas-ai-api
Componente: waitress
Status: Firing

⚠️ Ação Necessária: Verificar performance do servidor e possível sobrecarga.
```

## 🔍 Troubleshooting

### Problema: Autenticação falha
**Solução**: Execute `./testar-autenticacao.sh` e verifique as credenciais

### Problema: Dashboard não carrega
**Solução**: Verifique se o data source Loki está configurado corretamente

### Problema: Alerta não dispara
**Solução**: 
1. Teste a query no Explore
2. Verifique se há logs com o padrão esperado
3. Execute `./verificar-alertas-v2.sh`

### Problema: Webhook não funciona
**Solução**: Execute `./test-google-chat-webhook.sh` para testar

## 📈 Monitoramento Contínuo

### Verificações Regulares
- **Dashboard**: Acompanhar métricas diariamente
- **Alertas**: Verificar se estão ativos
- **Logs**: Monitorar padrões de warnings

### Manutenção
- **Ajustar threshold**: Modificar de 5 para outro valor se necessário
- **Expandir monitoramento**: Adicionar outros tipos de alertas
- **Otimizar queries**: Melhorar performance se necessário

## 🔧 Configuração Avançada

### Modificar Threshold
Para alterar o limite de 5 warnings:
1. Edite o arquivo `criar-alertas-waitress.sh`
2. Modifique a condição na Alert Rule
3. Execute o script novamente

### Adicionar Novos Alertas
1. Copie a estrutura da Alert Rule existente
2. Modifique a query e condições
3. Adicione ao script principal

### Personalizar Mensagens
Edite o Contact Point no script `criar-alertas-waitress.sh` na seção de configuração do webhook.

## 📝 Logs e Debugging

### Verificar Logs do Grafana
```bash
kubectl logs -n monitoring deployment/grafana
```

### Verificar Logs da Aplicação
```bash
kubectl logs -n conversas-ai deployment/conversas-ai-api --tail=100
```

### Debug de Queries
Use o Explore do Grafana para testar e debugar queries Loki.

---

## 🎉 Status Atual

✅ **Sistema 100% Funcional**
- Dashboard criado e funcionando
- Contact Point configurado e testado
- Alert Rule ativa sem erros
- Webhook enviando mensagens
- Monitoramento ativo 24/7

**O sistema está pronto para detectar e alertar sobre problemas de performance no Waitress Queue!**
