#!/bin/bash

# Script para criar dashboard de monitoramento do Nginx Ingress para API Conversas AI
# Monitora requests HTTP para api.conversas.ai através dos logs do Nginx Ingress Controller
# Uso: ./criar-dashboard-nginx-ingress.sh

set -e

# Carregar configuração de autenticação
if [[ -f "auth-config.sh" ]]; then
    source auth-config.sh
    echo "✅ Configuração de autenticação carregada"
else
    echo "❌ Execute ./testar-autenticacao.sh primeiro"
    exit 1
fi

echo ""
echo "🌐 Criando Dashboard de Monitoramento Nginx Ingress - API Conversas AI..."
echo "======================================================================="
echo ""

# Função para fazer chamadas autenticadas
call_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo "🔄 $description"
    echo "   $method /api/$endpoint"
    
    if [[ -n "$data" ]]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$GRAFANA_URL/api/$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            "$GRAFANA_URL/api/$endpoint")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "   Status: HTTP $http_code"
    
    if [[ $http_code -ge 200 && $http_code -lt 300 ]]; then
        echo "   ✅ Sucesso!"
        return 0
    else
        echo "   ❌ Erro: $body"
        return 1
    fi
}

# 1. Verificar se o data source Loki está disponível
echo "🔍 Passo 1: Verificando Data Source Loki..."
if call_api "GET" "datasources" "" "Verificando data sources"; then
    echo "✅ Data sources verificados"
else
    echo "❌ Erro ao verificar data sources"
    exit 1
fi
echo ""

# 2. Importar Dashboard do Nginx Ingress
echo "🌐 Passo 2: Importando Dashboard Nginx Ingress..."

DASHBOARD_JSON=$(cat grafana-dashboards/nginx-ingress-api-monitoring.json)
if call_api "POST" "dashboards/db" "$DASHBOARD_JSON" "Importando Dashboard Nginx Ingress"; then
    echo "✅ Dashboard Nginx Ingress importado com sucesso!"
    dashboard_created=true
else
    echo "⚠️  Dashboard pode já existir, tentando atualizar..."
    
    # Tentar atualizar dashboard existente
    if call_api "POST" "dashboards/db" "$DASHBOARD_JSON" "Atualizando Dashboard Nginx Ingress"; then
        echo "✅ Dashboard Nginx Ingress atualizado!"
        dashboard_created=true
    else
        echo "❌ Falha ao criar/atualizar dashboard"
        dashboard_created=false
    fi
fi
echo ""

# 3. Verificar se o dashboard foi criado
echo "🔍 Passo 3: Verificando Dashboard criado..."
if call_api "GET" "dashboards/uid/nginx-ingress-api-conversas-ai" "" "Verificando dashboard"; then
    echo "✅ Dashboard acessível"
else
    echo "❌ Dashboard não encontrado"
fi
echo ""

# 4. Testar queries do Nginx Ingress no Loki
echo "🧪 Passo 4: Testando queries do Nginx Ingress no Loki..."

# Testar query básica de logs do Nginx Ingress
echo "   Testando query de logs do Nginx Ingress..."
nginx_test=$(curl -s -H "$AUTH_HEADER" \
    "$GRAFANA_URL/api/datasources/proxy/uid/cen20vehcfpq8b/loki/api/v1/query?query=%7Bapp%3D%22ingress-nginx%22%7D&limit=1" 2>/dev/null || echo "{}")

if echo "$nginx_test" | grep -q "data"; then
    echo "   ✅ Query básica do Nginx funcionando"
    
    # Verificar se há logs específicos da API conversas.ai
    api_logs_test=$(curl -s -H "$AUTH_HEADER" \
        "$GRAFANA_URL/api/datasources/proxy/uid/cen20vehcfpq8b/loki/api/v1/query?query=%7Bapp%3D%22ingress-nginx%22%7D%20%7C%3D%20%22api.conversas.ai%22&limit=1" 2>/dev/null || echo "{}")
    
    if echo "$api_logs_test" | grep -q '"result":\['; then
        echo "   ✅ Logs da API api.conversas.ai encontrados"
    else
        echo "   ⚠️  Nenhum log da API encontrado (pode ser normal se não há tráfego)"
    fi
else
    echo "   ❌ Erro ao executar query no Loki para Nginx Ingress"
fi
echo ""

# 5. Verificar se há logs do controller do ingress-nginx
echo "🔍 Passo 5: Verificando logs do controller ingress-nginx..."
controller_test=$(curl -s -H "$AUTH_HEADER" \
    "$GRAFANA_URL/api/datasources/proxy/uid/cen20vehcfpq8b/loki/api/v1/query?query=%7Bapp%3D%22ingress-nginx%22%2C%20container%3D%22controller%22%7D&limit=1" 2>/dev/null || echo "{}")

if echo "$controller_test" | grep -q "data"; then
    echo "   ✅ Logs do controller ingress-nginx encontrados"
else
    echo "   ⚠️  Logs do controller não encontrados - verifique se o ingress-nginx está enviando logs para Loki"
fi
echo ""

echo "🎉 Dashboard Nginx Ingress criado com sucesso!"
echo ""

if [[ "${dashboard_created:-false}" == "true" ]]; then
    echo "✅ Dashboard funcionando: nginx-ingress-api-conversas-ai"
    echo ""
    echo "🔗 URLs importantes:"
    echo "   • Dashboard Nginx Ingress: https://grafana.conversas.ai/d/nginx-ingress-api-conversas-ai"
    echo "   • Grafana Home: https://grafana.conversas.ai/"
    echo "   • Explore Loki: https://grafana.conversas.ai/explore"
    echo ""
    echo "📊 Métricas disponíveis:"
    echo "   ✅ Total de requests (últimos 5 minutos)"
    echo "   ✅ Requests por segundo"
    echo "   ✅ Distribuição de códigos HTTP de status"
    echo "   ✅ Tempo de resposta P95"
    echo "   ✅ Taxa de erro (4xx + 5xx)"
    echo "   ✅ Distribuição de tempos de resposta (P50, P95, P99)"
    echo "   ✅ Top endpoints mais acessados"
    echo "   ✅ Distribuição de métodos HTTP"
    echo "   ✅ Tamanho de requests e responses"
    echo "   ✅ Logs recentes do Nginx Ingress"
    echo ""
    echo "🧪 Para testar:"
    echo "   1. Acesse o dashboard para visualizar métricas"
    echo "   2. Faça algumas requisições para api.conversas.ai"
    echo "   3. Observe as métricas sendo atualizadas em tempo real"
    echo ""
    echo "🔍 Queries de exemplo para Explore:"
    echo "   • Total requests: sum(count_over_time({app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" [5m]))"
    echo "   • Requests por segundo: sum(rate({app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" [1m]))"
    echo "   • Logs do Nginx: {app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\""
    echo "   • Tempo de resposta: {app=\"ingress-nginx\"} |= \"api.conversas.ai\" | regexp \`request_time=(?P<request_time>[\\d\\.]+)\` | unwrap request_time"
else
    echo "❌ Não foi possível criar o dashboard"
    echo ""
    echo "📋 Solução alternativa:"
    echo "   1. Acesse: https://grafana.conversas.ai/dashboard/import"
    echo "   2. Cole o conteúdo de: grafana-dashboards/nginx-ingress-api-monitoring.json"
    echo "   3. Configure manualmente o dashboard"
fi

echo ""
echo "💡 Dicas importantes:"
echo "   • O dashboard monitora logs do Nginx Ingress Controller"
echo "   • As métricas são extraídas dos logs de acesso do Nginx"
echo "   • Certifique-se de que o ingress-nginx está enviando logs para Loki"
echo "   • Use o Explore do Grafana para testar e ajustar queries"
echo "   • Os logs devem conter informações como request_time, status, etc."

echo ""
echo "🌐 Monitoramento ativo para api.conversas.ai via Nginx Ingress!"
