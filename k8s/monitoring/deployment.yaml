apiVersion: apps/v1
kind: Deployment
metadata:
  name: monitoring
  labels:
    app: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: monitoring
  template:
    metadata:
      labels:
        app: monitoring
    spec:
      containers:
      - name: monitoring
        image: registry.gitlab.com/plataformazw/ia/orion/monitoring:main
        ports:
        - containerPort: 8083
        envFrom:
        - configMapRef:
            name: monitoring-config
        - secretRef:
            name: monitoring-secrets
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "300m"
            memory: "256Mi"
        livenessProbe:
          httpGet:
            path: /health_check
            port: 8083
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health_check
            port: 8083
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: monitoring-service
  labels:
    app: monitoring
spec:
  selector:
    app: monitoring
  ports:
  - port: 8083
    targetPort: 8083
  type: ClusterIP
