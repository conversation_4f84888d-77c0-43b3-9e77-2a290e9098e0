loki:
  # 1) segurança
  podSecurityContext:
    fsGroup: 10001          # <-- garante RW para o volume
    runAsNonRoot: true
    runAsUser: 10001
    runAsGroup: 10001

  containerSecurityContext:
    readOnlyRootFilesystem: false   # já resolvemos o RO

  # 2) volume
  persistence:
    enabled: true
    storageClassName: standard
    size: 30Gi
    mountPath: /var/loki           # (default)

  # 3) retenção de 4 dias
  config:
    limits_config:
      retention_period: 96h
    compactor:
      retention_enabled: true
      working_directory: /var/loki/compactor
      shared_store: filesystem
