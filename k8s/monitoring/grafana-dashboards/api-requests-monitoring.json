{"dashboard": {"uid": "api-requests-conversas-ai", "title": "API Requests Monitoring - Conversas AI", "tags": ["conversas-ai", "api", "requests", "monitoring"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Total Requests (Last 5 minutes)", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum(count_over_time({app=\"conversas-ai-api\"} |~ \"(GET|POST|PUT|DELETE|PATCH)\" [5m]))", "legendFormat": "Total Requests", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}}}, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal"}}, {"id": 2, "title": "Requests per Minute", "type": "timeseries", "gridPos": {"h": 8, "w": 18, "x": 6, "y": 0}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum(rate({app=\"conversas-ai-api\"} |~ \"(GET|POST|PUT|DELETE|PATCH)\" [1m]))", "legendFormat": "Requests/min", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}}, {"id": 3, "title": "HTTP Status Codes (Last 15 minutes)", "type": "piechart", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum by (status_code) (count_over_time({app=\"conversas-ai-api\"} | regexp `(?P<method>GET|POST|PUT|DELETE|PATCH).*(?P<status_code>[1-5]\\d{2})` [15m]))", "legendFormat": "{{status_code}}", "refId": "A"}], "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "pieType": "pie", "tooltip": {"mode": "single"}, "legend": {"displayMode": "visible", "placement": "right"}}}, {"id": 4, "title": "HTTP Methods Distribution", "type": "barchart", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 8}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum by (method) (count_over_time({app=\"conversas-ai-api\"} | regexp `(?P<method>GET|POST|PUT|DELETE|PATCH)` [15m]))", "legendFormat": "{{method}}", "refId": "A"}], "options": {"orientation": "horizontal", "barWidth": 0.97, "groupWidth": 0.7, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}}, {"id": 5, "title": "Error Rate (4xx + 5xx)", "type": "stat", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum(count_over_time({app=\"conversas-ai-api\"} | regexp `(?P<method>GET|POST|PUT|DELETE|PATCH).*(?P<status_code>[4-5]\\d{2})` [5m])) / sum(count_over_time({app=\"conversas-ai-api\"} | regexp `(?P<method>GET|POST|PUT|DELETE|PATCH).*(?P<status_code>[1-5]\\d{2})` [5m])) * 100", "legendFormat": "Error Rate %", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 10}]}}}, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center"}}, {"id": 6, "title": "Top Endpoints (Last 15 minutes)", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "topk(10, sum by (endpoint) (count_over_time({app=\"conversas-ai-api\"} | regexp `(?P<method>GET|POST|PUT|DELETE|PATCH)\\s+(?P<endpoint>/[^\\s]*)` [15m])))", "legendFormat": "{{endpoint}}", "refId": "A"}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"endpoint": "Endpoint", "Value": "Requests"}}}], "options": {"showHeader": true}}, {"id": 7, "title": "Response Time Distribution", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "quantile_over_time(0.50, {app=\"conversas-ai-api\"} | regexp `(?P<method>GET|POST|PUT|DELETE|PATCH).*(?P<response_time>\\d+)ms` | unwrap response_time | __error__=\"\" [5m])", "legendFormat": "P50 Response Time", "refId": "A"}, {"expr": "quantile_over_time(0.95, {app=\"conversas-ai-api\"} | regexp `(?P<method>GET|POST|PUT|DELETE|PATCH).*(?P<response_time>\\d+)ms` | unwrap response_time | __error__=\"\" [5m])", "legendFormat": "P95 Response Time", "refId": "B"}, {"expr": "quantile_over_time(0.99, {app=\"conversas-ai-api\"} | regexp `(?P<method>GET|POST|PUT|DELETE|PATCH).*(?P<response_time>\\d+)ms` | unwrap response_time | __error__=\"\" [5m])", "legendFormat": "P99 Response Time", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "ms", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 3000}]}}}}, {"id": 8, "title": "Recent API Requests", "type": "logs", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "{app=\"conversas-ai-api\"} |~ \"(GET|POST|PUT|DELETE|PATCH)\"", "refId": "A"}], "options": {"showTime": true, "showLabels": false, "showCommonLabels": true, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}}]}, "folderId": 0, "overwrite": true}