{"dashboard": {"uid": "nginx-ingress-api-conversas-ai", "title": "Nginx Ingress API Monitoring - Conversas AI", "tags": ["conversas-ai", "nginx", "ingress", "api", "requests"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Total Requests (Last 5 minutes)", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum(count_over_time({app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" != \"/api/\" != \"/datasources/\" != \"/query\" != \"/explore\" != \"/dashboard\" [5m]))", "legendFormat": "Total Requests", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}}}, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal"}}, {"id": 2, "title": "Requests per Second", "type": "timeseries", "gridPos": {"h": 8, "w": 18, "x": 6, "y": 0}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum(rate({app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" != \"/api/\" != \"/datasources/\" != \"/query\" != \"/explore\" != \"/dashboard\" [1m]))", "legendFormat": "Requests/sec", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}}, {"id": 3, "title": "HTTP Status Codes Distribution", "type": "piechart", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum by (status) (count_over_time({app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" | regexp `\"(?P<method>GET|POST|PUT|DELETE|PATCH)\\s+(?P<path>/[^\\s]*?)\\s+HTTP/[\\d\\.]+\"\\s+(?P<status>\\d{3})` | path !~ \"/api/(datasources|query|ds|explore|dashboard).*\" [15m]))", "legendFormat": "{{status}}", "refId": "A"}], "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "pieType": "pie", "tooltip": {"mode": "single"}, "legend": {"displayMode": "visible", "placement": "right"}}}, {"id": 4, "title": "Response Time P95 (ms)", "type": "stat", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 8}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "quantile_over_time(0.95, {app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" | regexp `request_time=(?P<request_time>[\\d\\.]+)` | unwrap request_time | __error__=\"\" [5m]) * 1000", "legendFormat": "P95 Response Time", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ms", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 3000}]}}}, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center"}}, {"id": 5, "title": "Error Rate (4xx + 5xx)", "type": "stat", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum(count_over_time({app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" | regexp `(?P<method>\\w+)\\s+(?P<path>\\S+)\\s+\\S+\\s+(?P<status>[4-5]\\d{2})` [5m])) / sum(count_over_time({app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" | regexp `(?P<method>\\w+)\\s+(?P<path>\\S+)\\s+\\S+\\s+(?P<status>\\d{3})` [5m])) * 100", "legendFormat": "Error Rate %", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 10}]}}}, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center"}}, {"id": 6, "title": "Response Time Distribution", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "quantile_over_time(0.50, {app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" | regexp `request_time=(?P<request_time>[\\d\\.]+)` | unwrap request_time | __error__=\"\" [5m]) * 1000", "legendFormat": "P50 Response Time", "refId": "A"}, {"expr": "quantile_over_time(0.95, {app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" | regexp `request_time=(?P<request_time>[\\d\\.]+)` | unwrap request_time | __error__=\"\" [5m]) * 1000", "legendFormat": "P95 Response Time", "refId": "B"}, {"expr": "quantile_over_time(0.99, {app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" | regexp `request_time=(?P<request_time>[\\d\\.]+)` | unwrap request_time | __error__=\"\" [5m]) * 1000", "legendFormat": "P99 Response Time", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "ms", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 3000}]}}}}, {"id": 7, "title": "Top Endpoints (Last 15 minutes)", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "topk(10, sum by (path) (count_over_time({app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" | regexp `(?P<method>\\w+)\\s+(?P<path>/[^\\s?]*)` [15m])))", "legendFormat": "{{path}}", "refId": "A"}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"path": "Endpoint", "Value": "Requests"}}}], "options": {"showHeader": true}}, {"id": 8, "title": "HTTP Methods Distribution", "type": "barchart", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 24}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum by (method) (count_over_time({app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" | regexp `(?P<method>GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)` [15m]))", "legendFormat": "{{method}}", "refId": "A"}], "options": {"orientation": "horizontal", "barWidth": 0.97, "groupWidth": 0.7}}, {"id": 9, "title": "Request Size Distribution", "type": "timeseries", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 24}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "quantile_over_time(0.95, {app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" | regexp `request_length=(?P<request_length>\\d+)` | unwrap request_length | __error__=\"\" [5m])", "legendFormat": "P95 Request Size", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "bytes"}}}, {"id": 10, "title": "Response Size Distribution", "type": "timeseries", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 24}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "quantile_over_time(0.95, {app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\" | regexp `bytes_sent=(?P<bytes_sent>\\d+)` | unwrap bytes_sent | __error__=\"\" [5m])", "legendFormat": "P95 Response Size", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "bytes"}}}, {"id": 11, "title": "Recent Nginx Access Logs - api.conversas.ai", "type": "logs", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "{app=\"ingress-nginx\", container=\"controller\"} |= \"api.conversas.ai\"", "refId": "A"}], "options": {"showTime": true, "showLabels": false, "showCommonLabels": true, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}}]}, "folderId": 0, "overwrite": true}