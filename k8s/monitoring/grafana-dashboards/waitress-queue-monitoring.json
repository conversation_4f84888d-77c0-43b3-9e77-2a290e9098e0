{"dashboard": {"uid": "waitress-queue-monitoring", "title": "Waitress Queue Monitoring - Conversas AI", "tags": ["conversas-ai", "waitress", "monitoring", "alerts"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Waitress Queue Task Warnings Count (Last 5 minutes)", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum(count_over_time({app=\"conversas-ai-api\"} |= \"WARNING:waitress.queue:Task\" [5m]))", "legendFormat": "Waitress Queue Warnings", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 3}, {"color": "red", "value": 5}]}}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "background", "graphMode": "area", "justifyMode": "auto"}}, {"id": 2, "title": "Waitress Queue Warnings Over Time", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum(count_over_time({app=\"conversas-ai-api\"} |= \"WARNING:waitress.queue:Task\" [1m]))", "legendFormat": "Warnings per minute", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 3}, {"color": "red", "value": 5}]}}}}, {"id": 3, "title": "Recent Waitress Queue Warning Logs", "type": "logs", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "{app=\"conversas-ai-api\"} |= \"WARNING:waitress.queue:Task\"", "refId": "A"}], "options": {"showTime": true, "showLabels": false, "showCommonLabels": true, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}}]}, "folderId": 0, "overwrite": true}