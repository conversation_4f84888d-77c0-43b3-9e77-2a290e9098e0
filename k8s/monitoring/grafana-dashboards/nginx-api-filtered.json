{"dashboard": {"uid": "nginx-api-filtered-conversas-ai", "title": "API Requests with Filters - Conversas AI", "tags": ["conversas-ai", "nginx", "ingress", "api", "filtered"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "templating": {"list": [{"name": "method", "type": "custom", "label": "HTTP Method", "query": "All,GET,POST,PUT,DELETE,PATCH", "current": {"selected": true, "text": "All", "value": "All"}, "options": [{"text": "All", "value": "All", "selected": true}, {"text": "GET", "value": "GET", "selected": false}, {"text": "POST", "value": "POST", "selected": false}, {"text": "PUT", "value": "PUT", "selected": false}, {"text": "DELETE", "value": "DELETE", "selected": false}, {"text": "PATCH", "value": "PATCH", "selected": false}], "multi": false, "includeAll": false, "hide": 0}, {"name": "status_code", "type": "custom", "label": "Status Code", "query": "All,2xx,3xx,4xx,5xx", "current": {"selected": true, "text": "All", "value": "All"}, "options": [{"text": "All", "value": "All", "selected": true}, {"text": "2xx Success", "value": "2", "selected": false}, {"text": "3xx Redirect", "value": "3", "selected": false}, {"text": "4xx Client Error", "value": "4", "selected": false}, {"text": "5xx Server Error", "value": "5", "selected": false}], "multi": false, "includeAll": false, "hide": 0}]}, "panels": [{"id": 1, "title": "Total API Requests (Filtered)", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum(count_over_time({app=\"ingress-nginx\", container=\"controller\"} |= \"conversas-ai-conversas-ai-api-service\" |~ \"(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS) /\" [5m]))", "legendFormat": "API Requests", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 200}]}}}, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center"}}, {"id": 2, "title": "API Requests per Second", "type": "timeseries", "gridPos": {"h": 8, "w": 18, "x": 6, "y": 0}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum(rate({app=\"ingress-nginx\", container=\"controller\"} |= \"conversas-ai-conversas-ai-api-service\" |~ \"(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS) /\" [1m]))", "legendFormat": "API Requests/sec", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 20}]}}}}, {"id": 3, "title": "Response Time P95", "type": "stat", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "quantile_over_time(0.95, {app=\"ingress-nginx\", container=\"controller\"} |= \"conversas-ai-conversas-ai-api-service\" | regexp `\\s+(?P<request_time>[\\d\\.]+)\\s+\\[conversas-ai-conversas-ai-api-service` | unwrap request_time | __error__=\"\" [5m]) * 1000", "legendFormat": "P95 Response Time", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ms", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 500}, {"color": "red", "value": 2000}]}}}, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center"}}, {"id": 4, "title": "Error Rate (4xx + 5xx)", "type": "stat", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 8}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum(count_over_time({app=\"ingress-nginx\", container=\"controller\"} |= \"conversas-ai-conversas-ai-api-service\" | regexp `HTTP/[\\d\\.]+\"\\s+(?P<status>[4-5]\\d{2})` [5m])) / sum(count_over_time({app=\"ingress-nginx\", container=\"controller\"} |= \"conversas-ai-conversas-ai-api-service\" | regexp `HTTP/[\\d\\.]+\"\\s+(?P<status>\\d{3})` [5m])) * 100", "legendFormat": "Error Rate %", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 2}, {"color": "red", "value": 5}]}}}, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center"}}, {"id": 5, "title": "HTTP Methods Distribution", "type": "piechart", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum by (method) (count_over_time({app=\"ingress-nginx\", container=\"controller\"} |= \"conversas-ai-conversas-ai-api-service\" | regexp `\"(?P<method>GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)` [15m]))", "legendFormat": "{{method}}", "refId": "A"}], "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "pieType": "pie", "tooltip": {"mode": "single"}, "legend": {"displayMode": "visible", "placement": "right"}}}, {"id": 6, "title": "Response Time Distribution", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "quantile_over_time(0.50, {app=\"ingress-nginx\", container=\"controller\"} |= \"conversas-ai-conversas-ai-api-service\" | regexp `\\s+(?P<request_time>[\\d\\.]+)\\s+\\[conversas-ai-conversas-ai-api-service` | unwrap request_time | __error__=\"\" [5m]) * 1000", "legendFormat": "P50 Response Time", "refId": "A"}, {"expr": "quantile_over_time(0.95, {app=\"ingress-nginx\", container=\"controller\"} |= \"conversas-ai-conversas-ai-api-service\" | regexp `\\s+(?P<request_time>[\\d\\.]+)\\s+\\[conversas-ai-conversas-ai-api-service` | unwrap request_time | __error__=\"\" [5m]) * 1000", "legendFormat": "P95 Response Time", "refId": "B"}, {"expr": "quantile_over_time(0.99, {app=\"ingress-nginx\", container=\"controller\"} |= \"conversas-ai-conversas-ai-api-service\" | regexp `\\s+(?P<request_time>[\\d\\.]+)\\s+\\[conversas-ai-conversas-ai-api-service` | unwrap request_time | __error__=\"\" [5m]) * 1000", "legendFormat": "P99 Response Time", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "ms", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 500}, {"color": "red", "value": 2000}]}}}}, {"id": 7, "title": "Top API Endpoints", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "topk(10, sum by (endpoint) (count_over_time({app=\"ingress-nginx\", container=\"controller\"} |= \"conversas-ai-conversas-ai-api-service\" | regexp `\"(?P<method>\\w+)\\s+(?P<endpoint>/[^\\s?]*)` [15m])))", "legendFormat": "{{endpoint}}", "refId": "A"}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"endpoint": "API Endpoint", "Value": "Requests"}}}], "options": {"showHeader": true}}, {"id": 8, "title": "Recent API Requests (Clean Logs)", "type": "logs", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "{app=\"ingress-nginx\", container=\"controller\"} |= \"conversas-ai-conversas-ai-api-service\"", "refId": "A"}], "options": {"showTime": true, "showLabels": false, "showCommonLabels": true, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}}]}, "folderId": 0, "overwrite": true}