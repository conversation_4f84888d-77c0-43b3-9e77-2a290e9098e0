#!/bin/bash

# Script para testar o webhook do Google Chat
# Uso: ./test-google-chat-webhook.sh

set -e

WEBHOOK_URL="https://chat.googleapis.com/v1/spaces/AAQAFsPVSsM/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=rUf8aTLJUl-TpFT4RYLdIcZVJ6WRx91LlFUIRUJneRM"

echo "🧪 Testando webhook do Google Chat..."

# Mensagem de teste
TEST_MESSAGE='{
  "text": "🧪 **TESTE DE ALERTA - Conversas AI**\n\n**Serviço**: conversas-ai-api\n**Componente**: waitress\n**Status**: Teste\n**Horário**: '$(date)'\n\nEste é um teste do sistema de alertas do Grafana. Se você recebeu esta mensagem, o webhook está funcionando corretamente! ✅"
}'

echo "📤 Enviando mensagem de teste..."

# Fazer a requisição
response=$(curl -s -w "\n%{http_code}" -X POST \
  -H "Content-Type: application/json" \
  -d "$TEST_MESSAGE" \
  "$WEBHOOK_URL")

# Extrair código HTTP e corpo da resposta
http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | head -n -1)

echo ""
echo "📊 Resultado:"
echo "   HTTP Code: $http_code"

if [[ $http_code -eq 200 ]]; then
    echo "   Status: ✅ Sucesso!"
    echo "   Resposta: $body"
    echo ""
    echo "🎉 Webhook funcionando corretamente!"
    echo "   Verifique o Google Chat para confirmar que a mensagem foi recebida."
else
    echo "   Status: ❌ Erro!"
    echo "   Resposta: $body"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "   1. Verifique se a URL do webhook está correta"
    echo "   2. Confirme se o bot tem permissão para enviar mensagens"
    echo "   3. Verifique se o espaço do Google Chat ainda existe"
fi

echo ""
echo "📋 Informações do webhook:"
echo "   URL: $WEBHOOK_URL"
echo "   Método: POST"
echo "   Content-Type: application/json"
