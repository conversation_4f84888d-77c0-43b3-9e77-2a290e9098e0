#!/bin/bash

# Script para criar dashboard com filtros de monitoramento da API Conversas AI
# Dashboard com filtros para método HTTP e código de status
# Uso: ./criar-dashboard-com-filtros.sh

set -e

# Carregar configuração de autenticação
if [[ -f "auth-config.sh" ]]; then
    source auth-config.sh
    echo "✅ Configuração de autenticação carregada"
else
    echo "❌ Execute ./testar-autenticacao.sh primeiro"
    exit 1
fi

echo ""
echo "🎛️  Criando Dashboard COM FILTROS - API Conversas AI..."
echo "===================================================="
echo ""

# Função para fazer chamadas autenticadas
call_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo "🔄 $description"
    echo "   $method /api/$endpoint"
    
    if [[ -n "$data" ]]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$GRAFANA_URL/api/$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            "$GRAFANA_URL/api/$endpoint")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "   Status: HTTP $http_code"
    
    if [[ $http_code -ge 200 && $http_code -lt 300 ]]; then
        echo "   ✅ Sucesso!"
        return 0
    else
        echo "   ❌ Erro: $body"
        return 1
    fi
}

# 1. Remover dashboards antigos
echo "🗑️  Passo 1: Removendo dashboards antigos..."
for dashboard_uid in "nginx-api-clean-conversas-ai" "nginx-ingress-api-conversas-ai"; do
    echo "   Removendo: $dashboard_uid"
    call_api "DELETE" "dashboards/uid/$dashboard_uid" "" "Removendo $dashboard_uid" || echo "   (Dashboard pode não existir)"
done
echo ""

# 2. Importar Dashboard com Filtros
echo "🎛️  Passo 2: Importando Dashboard com Filtros..."

DASHBOARD_JSON=$(cat grafana-dashboards/nginx-api-filtered.json)
if call_api "POST" "dashboards/db" "$DASHBOARD_JSON" "Importando Dashboard com Filtros"; then
    echo "✅ Dashboard com Filtros importado com sucesso!"
    dashboard_created=true
else
    echo "❌ Falha ao criar dashboard com filtros"
    dashboard_created=false
fi
echo ""

# 3. Verificar se o dashboard foi criado
echo "🔍 Passo 3: Verificando Dashboard criado..."
if call_api "GET" "dashboards/uid/nginx-api-filtered-conversas-ai" "" "Verificando dashboard"; then
    echo "✅ Dashboard acessível"
else
    echo "❌ Dashboard não encontrado"
fi
echo ""

# 4. Testar queries básicas
echo "🧪 Passo 4: Testando queries básicas..."

# Testar query básica
echo "   Testando query básica..."
basic_test=$(curl -s -H "$AUTH_HEADER" \
    "$GRAFANA_URL/api/datasources/proxy/uid/cen20vehcfpq8b/loki/api/v1/query?query=%7Bapp%3D%22ingress-nginx%22%7D%20%7C%3D%20%22conversas-ai-conversas-ai-api-service%22&limit=3" 2>/dev/null || echo "{}")

if echo "$basic_test" | grep -q "data"; then
    echo "   ✅ Query básica funcionando"
    
    # Verificar se há dados
    if echo "$basic_test" | grep -q '"result":\['; then
        echo "   ✅ Dados encontrados nos logs"
    else
        echo "   ℹ️  Nenhum dado encontrado (normal se não há tráfego)"
    fi
else
    echo "   ❌ Erro ao executar query básica"
fi
echo ""

echo "🎉 Dashboard com Filtros criado com sucesso!"
echo ""

if [[ "${dashboard_created:-false}" == "true" ]]; then
    echo "✅ Dashboard funcionando: nginx-api-filtered-conversas-ai"
    echo ""
    echo "🔗 URLs importantes:"
    echo "   • Dashboard com Filtros: https://grafana.conversas.ai/d/nginx-api-filtered-conversas-ai"
    echo "   • Grafana Home: https://grafana.conversas.ai/"
    echo ""
    echo "🎛️  Filtros disponíveis:"
    echo "   📋 HTTP Method: All, GET, POST, PUT, DELETE, PATCH"
    echo "   📊 Status Code: All, 2xx, 3xx, 4xx, 5xx"
    echo ""
    echo "📊 Painéis disponíveis:"
    echo "   ✅ Total de requests da API (filtrado)"
    echo "   ✅ Requests por segundo"
    echo "   ✅ Tempo de resposta P95"
    echo "   ✅ Taxa de erro (4xx + 5xx)"
    echo "   ✅ Distribuição de métodos HTTP"
    echo "   ✅ Distribuição de tempos de resposta"
    echo "   ✅ Top endpoints da API"
    echo "   ✅ Logs recentes filtrados"
    echo ""
    echo "🎛️  Como usar os filtros:"
    echo "   1. Acesse o dashboard"
    echo "   2. Use os dropdowns no topo para filtrar:"
    echo "      • HTTP Method: Filtra por método (GET, POST, etc.)"
    echo "      • Status Code: Filtra por código de resposta"
    echo "   3. Os gráficos se atualizam automaticamente"
    echo ""
    echo "🧪 Para testar:"
    echo "   1. Acesse o dashboard"
    echo "   2. Selecione 'GET' no filtro HTTP Method"
    echo "   3. Observe apenas requests GET"
    echo "   4. Selecione '4xx' no filtro Status Code"
    echo "   5. Observe apenas erros 4xx"
else
    echo "❌ Não foi possível criar o dashboard com filtros"
fi

echo ""
echo "💡 Funcionalidades dos filtros:"
echo "   • Filtros são aplicados a todos os painéis"
echo "   • Filtros são interativos e em tempo real"
echo "   • Combinação de filtros para análise específica"
echo "   • Filtros persistem durante a sessão"

echo ""
echo "🎛️  Dashboard com filtros pronto para análise detalhada da API!"
