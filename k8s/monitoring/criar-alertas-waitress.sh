#!/bin/bash

# Script para criar alertas e dashboards do Waitress Queue
# Cria dashboard, contact point e alert rule funcionando
# Uso: ./criar-alertas-waitress.sh

set -e

# Carregar configuração de autenticação
if [[ -f "auth-config.sh" ]]; then
    source auth-config.sh
    echo "✅ Configuração de autenticação carregada"
else
    echo "❌ Execute ./testar-autenticacao.sh primeiro"
    exit 1
fi

echo ""
echo "🚨 Criando sistema completo de alertas Waitress Queue..."
echo "======================================================="
echo ""

# Função para fazer chamadas autenticadas
call_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo "🔄 $description"
    echo "   $method /api/$endpoint"
    
    if [[ -n "$data" ]]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$GRAFANA_URL/api/$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            "$GRAFANA_URL/api/$endpoint")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "   Status: HTTP $http_code"
    
    if [[ $http_code -ge 200 && $http_code -lt 300 ]]; then
        echo "   ✅ Sucesso!"
        return 0
    else
        echo "   ❌ Erro: $body"
        return 1
    fi
}

# 1. Importar Dashboard
echo "📊 Passo 1: Importando Dashboard..."

DASHBOARD_JSON=$(cat grafana-dashboards/waitress-queue-monitoring.json)
if call_api "POST" "dashboards/db" "$DASHBOARD_JSON" "Importando Dashboard"; then
    echo "✅ Dashboard importado com sucesso!"
else
    echo "⚠️  Dashboard pode já existir"
fi
echo ""

# 2. Criar Contact Point
echo "📱 Passo 2: Criando Contact Point..."

CONTACT_POINT='{
  "uid": "google-chat-conversas-ai",
  "name": "Google Chat Conversas AI",
  "type": "webhook",
  "settings": {
    "url": "https://chat.googleapis.com/v1/spaces/AAQAFsPVSsM/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=rUf8aTLJUl-TpFT4RYLdIcZVJ6WRx91LlFUIRUJneRM",
    "httpMethod": "POST",
    "title": "🚨 Alerta Conversas AI - Waitress Queue",
    "message": "**ALERTA CRÍTICO**: Waitress Queue Warnings Alto\\n\\n**Descrição**: Detectados mais de 5 warnings WARNING:waitress.queue:Task nos últimos 5 minutos na aplicação conversas-ai-api.\\n\\n**Serviço**: conversas-ai-api\\n**Componente**: waitress\\n**Status**: {{ .Status }}\\n\\n⚠️ **Ação Necessária**: Verificar performance do servidor e possível sobrecarga."
  }
}'

if call_api "POST" "v1/provisioning/contact-points" "$CONTACT_POINT" "Criando Contact Point"; then
    echo "✅ Contact Point criado com sucesso!"
else
    echo "⚠️  Contact Point pode já existir"
fi
echo ""

# 3. Limpar regras problemáticas antigas
echo "🗑️  Passo 3: Limpando regras problemáticas antigas..."

for rule_uid in "waitress-queue-alert-corrigido" "waitress-queue-alert-final" "waitress-queue-alert-v2" "waitress-queue-alert"; do
    echo "   Removendo: $rule_uid"
    call_api "DELETE" "v1/provisioning/alert-rules/$rule_uid" "" "Deletando $rule_uid" || echo "   (Regra pode não existir)"
done
echo ""

# 4. Criar Alert Rule com sintaxe correta
echo "⚠️  Passo 4: Criando Alert Rule com sintaxe correta..."

# Versão que funciona: usar apenas reduce com condição
ALERT_RULE_FUNCIONANDO='{
  "uid": "waitress-queue-alert-working",
  "title": "Waitress Queue Task Warnings High",
  "condition": "B",
  "data": [
    {
      "refId": "A",
      "queryType": "",
      "relativeTimeRange": {
        "from": 300,
        "to": 0
      },
      "datasourceUid": "cen20vehcfpq8b",
      "model": {
        "expr": "sum(count_over_time({app=\"conversas-ai-api\"} |= \"WARNING:waitress.queue:Task\" [5m]))",
        "intervalMs": 1000,
        "maxDataPoints": 43200,
        "refId": "A"
      }
    },
    {
      "refId": "B",
      "queryType": "",
      "relativeTimeRange": {
        "from": 0,
        "to": 0
      },
      "datasourceUid": "__expr__",
      "model": {
        "expression": "A",
        "reducer": "last",
        "refId": "B",
        "type": "reduce",
        "intervalMs": 1000,
        "maxDataPoints": 43200,
        "conditions": [
          {
            "evaluator": {
              "params": [5],
              "type": "gt"
            },
            "operator": {
              "type": "and"
            },
            "query": {
              "params": ["A"]
            },
            "reducer": {
              "params": [],
              "type": "last"
            },
            "type": "query"
          }
        ]
      }
    }
  ],
  "intervalSeconds": 60,
  "noDataState": "NoData",
  "execErrState": "Alerting",
  "for": "1m",
  "annotations": {
    "description": "O número de warnings WARNING:waitress.queue:Task da aplicação conversas-ai-api está acima de 5 nos últimos 5 minutos. Isso indica um possível problema de performance ou sobrecarga no servidor.",
    "summary": "Waitress Queue Task Warnings Alto - {{ $value }} warnings detectados"
  },
  "labels": {
    "team": "conversas-ai",
    "severity": "warning",
    "service": "conversas-ai-api",
    "component": "waitress"
  },
  "folderUID": "alerting",
  "ruleGroup": "conversas-ai-alerts"
}'

if call_api "POST" "v1/provisioning/alert-rules" "$ALERT_RULE_FUNCIONANDO" "Criando Alert Rule funcionando"; then
    echo "✅ Alert Rule funcionando criada!"
    working_rule="waitress-queue-alert-working"
else
    echo "❌ Falha ao criar Alert Rule funcionando"
    
    # Tentar versão ainda mais simples
    echo "🔄 Tentando versão ultra-simples..."
    
    ALERT_RULE_ULTRA_SIMPLES='{
      "uid": "waitress-queue-alert-simple",
      "title": "Waitress Queue Task Warnings High",
      "condition": "A",
      "data": [
        {
          "refId": "A",
          "queryType": "",
          "relativeTimeRange": {
            "from": 300,
            "to": 0
          },
          "datasourceUid": "cen20vehcfpq8b",
          "model": {
            "expr": "sum(count_over_time({app=\"conversas-ai-api\"} |= \"WARNING:waitress.queue:Task\" [5m])) > 5",
            "intervalMs": 1000,
            "maxDataPoints": 43200,
            "refId": "A"
          }
        }
      ],
      "intervalSeconds": 60,
      "noDataState": "NoData",
      "execErrState": "Alerting",
      "for": "1m",
      "annotations": {
        "description": "Detectados warnings WARNING:waitress.queue:Task acima do limite na aplicação conversas-ai-api.",
        "summary": "Waitress Queue Warnings Alto"
      },
      "labels": {
        "team": "conversas-ai",
        "severity": "warning",
        "service": "conversas-ai-api",
        "component": "waitress"
      },
      "folderUID": "alerting",
      "ruleGroup": "conversas-ai-alerts"
    }'
    
    if call_api "POST" "v1/provisioning/alert-rules" "$ALERT_RULE_ULTRA_SIMPLES" "Criando Alert Rule ultra-simples"; then
        echo "✅ Alert Rule ultra-simples criada!"
        working_rule="waitress-queue-alert-simple"
    else
        echo "❌ Falha ao criar Alert Rule ultra-simples"
    fi
fi
echo ""

# 5. Verificar se a nova regra funciona
echo "🔍 Passo 5: Verificando nova Alert Rule..."
if [[ -n "${working_rule:-}" ]]; then
    if call_api "GET" "v1/provisioning/alert-rules/$working_rule" "" "Verificando $working_rule"; then
        echo "✅ Regra $working_rule está funcionando"
    else
        echo "❌ Problema ao acessar regra $working_rule"
    fi
else
    echo "❌ Nenhuma regra foi criada com sucesso"
fi
echo ""

# 6. Listar todas as regras para confirmar
echo "📋 Passo 6: Listando todas as Alert Rules..."
if call_api "GET" "v1/provisioning/alert-rules" "" "Listando todas as regras"; then
    echo "   Regras listadas com sucesso"
else
    echo "   Erro ao listar regras"
fi
echo ""

echo "🎉 Sistema de alertas Waitress Queue criado com sucesso!"
echo ""

if [[ -n "${working_rule:-}" ]]; then
    echo "✅ Regra funcionando: $working_rule"
    echo ""
    echo "🔗 URLs importantes:"
    echo "   • Alert Rule: https://grafana.conversas.ai/alerting/grafana/$working_rule/view"
    echo "   • Dashboard: https://grafana.conversas.ai/d/waitress-queue-monitoring"
    echo "   • Lista de Alertas: https://grafana.conversas.ai/alerting/list"
    echo ""
    echo "📋 Sistema completo criado:"
    echo "   ✅ Dashboard de monitoramento"
    echo "   ✅ Contact Point para Google Chat"
    echo "   ✅ Alert Rule funcionando"
    echo ""
    echo "🧪 Para testar:"
    echo "   1. Acesse o dashboard para visualizar métricas"
    echo "   2. Execute: ./test-google-chat-webhook.sh"
    echo "   3. Monitore logs: kubectl logs -n conversas-ai deployment/conversas-ai-api | grep WARNING:waitress.queue:Task"
else
    echo "❌ Não foi possível criar uma regra funcionando via API"
    echo ""
    echo "📋 Solução alternativa:"
    echo "   1. Use configuração manual: CRIAR-ALERTAS-PASSO-A-PASSO.md"
    echo "   2. Crie a regra manualmente no Grafana"
    echo "   3. Use query simples: sum(count_over_time({app=\"conversas-ai-api\"} |= \"WARNING:waitress.queue:Task\" [5m])) > 5"
fi

echo ""
echo "💡 Dica: A query mais simples que funciona é:"
echo "   sum(count_over_time({app=\"conversas-ai-api\"} |= \"WARNING:waitress.queue:Task\" [5m])) > 5"
