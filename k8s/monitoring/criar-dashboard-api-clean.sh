#!/bin/bash

# Script para criar dashboard limpo de monitoramento da API Conversas AI
# Filtra apenas requests reais da API, excluindo requests do Grafana/Loki
# Uso: ./criar-dashboard-api-clean.sh

set -e

# Carregar configuração de autenticação
if [[ -f "auth-config.sh" ]]; then
    source auth-config.sh
    echo "✅ Configuração de autenticação carregada"
else
    echo "❌ Execute ./testar-autenticacao.sh primeiro"
    exit 1
fi

echo ""
echo "🧹 Criando Dashboard LIMPO de API Requests - Conversas AI..."
echo "=========================================================="
echo ""

# Função para fazer chamadas autenticadas
call_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo "🔄 $description"
    echo "   $method /api/$endpoint"
    
    if [[ -n "$data" ]]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$GRAFANA_URL/api/$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            "$GRAFANA_URL/api/$endpoint")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "   Status: HTTP $http_code"
    
    if [[ $http_code -ge 200 && $http_code -lt 300 ]]; then
        echo "   ✅ Sucesso!"
        return 0
    else
        echo "   ❌ Erro: $body"
        return 1
    fi
}

# 1. Remover dashboard antigo se existir
echo "🗑️  Passo 1: Removendo dashboard antigo..."
call_api "DELETE" "dashboards/uid/nginx-ingress-api-conversas-ai" "" "Removendo dashboard antigo" || echo "   (Dashboard antigo pode não existir)"
echo ""

# 2. Importar Dashboard Limpo
echo "🧹 Passo 2: Importando Dashboard Limpo..."

DASHBOARD_JSON=$(cat grafana-dashboards/nginx-ingress-api-clean.json)
if call_api "POST" "dashboards/db" "$DASHBOARD_JSON" "Importando Dashboard API Clean"; then
    echo "✅ Dashboard API Clean importado com sucesso!"
    dashboard_created=true
else
    echo "❌ Falha ao criar dashboard limpo"
    dashboard_created=false
fi
echo ""

# 3. Verificar se o dashboard foi criado
echo "🔍 Passo 3: Verificando Dashboard criado..."
if call_api "GET" "dashboards/uid/nginx-api-clean-conversas-ai" "" "Verificando dashboard"; then
    echo "✅ Dashboard acessível"
else
    echo "❌ Dashboard não encontrado"
fi
echo ""

# 4. Testar queries filtradas no Loki
echo "🧪 Passo 4: Testando queries filtradas no Loki..."

# Testar query filtrada
echo "   Testando query filtrada (sem requests do Grafana)..."
filtered_test=$(curl -s -H "$AUTH_HEADER" \
    "$GRAFANA_URL/api/datasources/proxy/uid/cen20vehcfpq8b/loki/api/v1/query?query=%7Bapp%3D%22ingress-nginx%22%7D%20%7C%3D%20%22api.conversas.ai%22%20%7C~%20%22(GET%7CPOST%7CPUT%7CDELETE%7CPATCH)%20/%22%20!%3D%20%22/api/datasources%22&limit=5" 2>/dev/null || echo "{}")

if echo "$filtered_test" | grep -q "data"; then
    echo "   ✅ Query filtrada funcionando"
    
    # Verificar se há resultados
    if echo "$filtered_test" | grep -q '"result":\['; then
        echo "   ✅ Requests reais da API encontrados"
    else
        echo "   ℹ️  Nenhum request real da API encontrado (normal se não há tráfego)"
    fi
else
    echo "   ❌ Erro ao executar query filtrada"
fi
echo ""

echo "🎉 Dashboard API Clean criado com sucesso!"
echo ""

if [[ "${dashboard_created:-false}" == "true" ]]; then
    echo "✅ Dashboard funcionando: nginx-api-clean-conversas-ai"
    echo ""
    echo "🔗 URLs importantes:"
    echo "   • Dashboard API Clean: https://grafana.conversas.ai/d/nginx-api-clean-conversas-ai"
    echo "   • Grafana Home: https://grafana.conversas.ai/"
    echo ""
    echo "🧹 Filtros aplicados:"
    echo "   ✅ Exclui requests para /api/datasources/"
    echo "   ✅ Exclui requests para /api/query"
    echo "   ✅ Exclui requests para /api/ds/"
    echo "   ✅ Exclui requests para /api/explore"
    echo "   ✅ Exclui requests para /api/dashboard"
    echo "   ✅ Mostra apenas requests reais da API"
    echo ""
    echo "📊 Métricas limpas disponíveis:"
    echo "   ✅ Total de requests da API (sem Grafana)"
    echo "   ✅ Requests por segundo da API"
    echo "   ✅ Tempo de resposta P95 da API"
    echo "   ✅ Taxa de erro da API (4xx + 5xx)"
    echo "   ✅ Códigos de status da API"
    echo "   ✅ Distribuição de tempos de resposta"
    echo "   ✅ Top endpoints da API"
    echo "   ✅ Logs recentes filtrados"
    echo ""
    echo "🧪 Para testar:"
    echo "   1. Acesse o dashboard limpo"
    echo "   2. Faça requests para api.conversas.ai (não para Grafana)"
    echo "   3. Observe apenas as métricas da API real"
    echo ""
    echo "🔍 Query de exemplo (filtrada):"
    echo "   {app=\"ingress-nginx\"} |= \"api.conversas.ai\" |~ \"(GET|POST|PUT|DELETE|PATCH) /\" != \"/api/datasources\" != \"/api/query\""
else
    echo "❌ Não foi possível criar o dashboard limpo"
fi

echo ""
echo "💡 Diferença do dashboard anterior:"
echo "   • Dashboard anterior: Mostrava TODAS as requests (incluindo Grafana)"
echo "   • Dashboard novo: Mostra APENAS requests reais da API"
echo "   • Filtros aplicados para excluir requests internas do Grafana/Loki"

echo ""
echo "🧹 Dashboard limpo pronto para monitorar apenas api.conversas.ai!"
