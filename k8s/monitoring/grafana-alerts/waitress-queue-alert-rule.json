{"alert": {"uid": "waitress-queue-alert", "title": "Waitress Queue Task Warnings High", "condition": "C", "data": [{"refId": "A", "queryType": "", "relativeTimeRange": {"from": 300, "to": 0}, "datasourceUid": "cen20vehcfpq8b", "model": {"expr": "sum(count_over_time({app=\"conversas-ai-api\"} |= \"WARNING:waitress.queue:Task\" [5m]))", "intervalMs": 1000, "maxDataPoints": 43200, "refId": "A"}}, {"refId": "B", "queryType": "", "relativeTimeRange": {"from": 0, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "A", "hide": false, "intervalMs": 1000, "maxDataPoints": 43200, "reducer": "last", "refId": "B", "type": "reduce"}}, {"refId": "C", "queryType": "", "relativeTimeRange": {"from": 0, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [5], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["C"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "B > 5", "hide": false, "intervalMs": 1000, "maxDataPoints": 43200, "refId": "C", "type": "threshold"}}], "intervalSeconds": 60, "maxDataPoints": 43200, "noDataState": "NoData", "execErrState": "Alerting", "for": "1m", "annotations": {"description": "O número de warnings 'WARNING:waitress.queue:Task' da aplicação conversas-ai-api está acima de 5 nos últimos 5 minutos. Isso indica um possível problema de performance ou sobrecarga no servidor.", "runbook_url": "", "summary": "Waitress Queue Task Warnings Alto - {{ $value }} warnings detectados"}, "labels": {"team": "conversas-ai", "severity": "warning", "service": "conversas-ai-api", "component": "waitress"}}, "folderUID": "alerting"}