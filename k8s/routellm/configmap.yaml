apiVersion: v1
kind: ConfigMap
metadata:
  name: routellm-config
  labels:
    app: routellm
data:
  REDIS_URL: "redis://redis-service:6379/0"
  FLOW_NAME: "Ponte"
  QUESTIONARY_FLOW_NAME: "Pesquisa de Satisfação / NPS"
  FLASK_ENV: "development"
  NUM_THREADS: "4"
  MODE: "router"
  ROUTER_TYPE: "mf"
  ROUTER_THRESHOLD: "0.35"
  WEAKER_MODEL: "GEMINI"
  STRONGER_MODEL: "OPEN_AI"
  OPEN_AI: "openai/gpt-4o-mini"
  GEMINI: "gemini/gemini-2.0-flash"
  TOGETHER: "together_ai/togethercomputer/LLaMA-2-7B-32K"
  BUCKET_NAME_AUDIO: "temporary_audios"
  HEALTHCHECK_PORT: "8082"
  ROLES_TO_KEEP_REDIS: "assistant, user, system"
  SECONDS_TO_WAIT_TILL_RESPONSE: "5"
  RETRY_CONTACT_TIME: "60"
  RETRY_MESSAGE: "false"
  LINK_APP_TREINO: "https://apptreino.com.br/#baixar"
  GCP_BIGQUERY_DATASET: "development"
