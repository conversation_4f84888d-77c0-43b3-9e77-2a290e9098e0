apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: redisinsight-ingress
  namespace: redis
  annotations:
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: redisinsight-auth
    nginx.ingress.kubernetes.io/auth-realm: "Redis Insight Authentication"
spec:
  ingressClassName: nginx
  rules:
  - host: redis.conversas.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: redisinsight-service
            port:
              number: 5540
