apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: redis
  labels:
    app: redis
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: redisinsight-service
  namespace: redis
  labels:
    app: redisinsight
spec:
  selector:
    app: redisinsight
  ports:
  - port: 5540
    targetPort: 5540
  type: ClusterIP