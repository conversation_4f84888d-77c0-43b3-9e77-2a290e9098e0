apiVersion: apps/v1
kind: Deployment
metadata:
  name: conversas-ai-worker-canary
  namespace: conversas-ai
  labels:
    app: conversas-ai-worker-canary
spec:
  replicas: 1
  selector:
    matchLabels:
      app: conversas-ai-worker-canary
  template:
    metadata:
      labels:
        app: conversas-ai-worker-canary
    spec:
      imagePullSecrets:
        - name: gitlab-registry-secret
      containers:
      - name: worker-canary
        image: registry.gitlab.com/plataformazw/ia/orion/worker:feature-ia-1354
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: conversas-ai-worker-config-canary
        - secretRef:
            name: conversas-ai-worker-secrets
        volumeMounts:
        - name: service-account-volume
          mountPath: /app/src/connections/conversas-ai.json
          subPath: conversas-ai.json
          readOnly: true
        resources:
        livenessProbe:
          httpGet:
            path: /health_check
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health_check
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
      volumes:
      - name: service-account-volume
        secret:
          secretName: conversas-ai-service-account
