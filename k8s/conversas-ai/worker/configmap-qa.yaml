apiVersion: v1
kind: ConfigMap
metadata:
  name: conversas-ai-worker-config-qa
  namespace: conversas-ai
  labels:
    app: conversas-ai-worker-qa
data:
  BASE_URL: "https://apiqa.conversas.ai"
  CHECK_PENDENCY: "false"
  DEBUG: "False"
  DOMAIN: "https://apiqa.conversas.ai"
  MESSENGER_API: z_api
  MODE: worker
  REDIS_URL: "redis://redis-service.redis.svc.cluster.local:6379/2"
  RETRY_CONTACT_TIME: "10"
  CONVERSATION_DATA_TTL: "20"
  RETRY_MESSAGE: "true"
  ROLES_TO_KEEP_REDIS: assistant, user, system
  SECONDS_TO_WAIT_TILL_RESPONSE: "10"
  Z_API_CLIENT_TOKEN: "F3b14fd459eb9462ebd51b359510e2c4dS"
  FLASK_ENV: "production"
  Z_API_INSTANCE_STATUS_CHECK_INTERVAL: "2"
  Z_API_INSTANCE_STATUS_CHECK: "True"
  BUCKET_NAME_AUDIO: "temporary_audios"
  PENDENCY_VERIFICATION_TIME: "60"
  LINK_APP_TREINO: "https://apptreino.com.br/#baixar"
  GCP_BIGQUERY_PROJECT_ID: "conversas-ai"
  GCP_BIGQUERY_DATASET: "production"
  HEALTHCHECK_PORT: "8081"
  JAEGER_HOST: "jaeger-service.jaeger.svc.cluster.local"
  JAEGER_PORT: "6831"
  BUFFER_SIZE: "30"
  MINUTES_UPDATE_CONTEXTO_EMPRESA: "20"
  QDRANT_HOST: "qdrant.qdrant.svc.cluster.local"
  QDRANT_PORT: "6333"
  DELAYED_QUEUE_MAX_WORKERS: "6"
