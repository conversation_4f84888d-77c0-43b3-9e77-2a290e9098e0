{"dashboard": {"uid": "aeoo8h2u0ne9sf", "title": "Performance Monitoring - Conversas AI Functions", "tags": ["conversas-ai", "performance", "functions"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Function Execution Time - P95 (Last 5 minutes)", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "quantile_over_time(0.95, {app=~\"conversas-ai-api|conversas-ai-worker\"} |= \"FUNC_TIME\" | regexp `\\\\[FUNC_TIME\\\\]-(?P<function_name>\\\\w+)-(?P<execution_time>\\\\d+)ms` | unwrap execution_time | __error__=\"\" [5m]) by (function_name)", "legendFormat": "{{function_name}} - P95", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ms", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 3000}]}}}}, {"id": 2, "title": "Function Execution Count (Last 5 minutes)", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "sum by (function_name) (count_over_time({app=~\"conversas-ai-api|conversas-ai-worker\"} |= \"FUNC_TIME\" | regexp `\\\\[FUNC_TIME\\\\]-(?P<function_name>\\\\w+)-(?P<execution_time>\\\\d+)ms` [5m]))", "legendFormat": "{{function_name}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short"}}}, {"id": 3, "title": "Average Function Performance Comparison", "type": "barchart", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "avg_over_time({app=~\"conversas-ai-api|conversas-ai-worker\"} |= \"FUNC_TIME\" | regexp `\\\\[FUNC_TIME\\\\]-(?P<function_name>\\\\w+)-(?P<execution_time>\\\\d+)ms` | unwrap execution_time | __error__=\"\" [15m]) by (function_name)", "legendFormat": "{{function_name}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ms", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 3000}]}}}}, {"id": 4, "title": "Function Performance Statistics", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "quantile_over_time(0.50, {app=~\"conversas-ai-api|conversas-ai-worker\"} |= \"FUNC_TIME\" | regexp `\\\\[FUNC_TIME\\\\]-(?P<function_name>\\\\w+)-(?P<execution_time>\\\\d+)ms` | unwrap execution_time | __error__=\"\" [15m]) by (function_name)", "legendFormat": "{{function_name}} - P50", "refId": "A"}, {"expr": "quantile_over_time(0.95, {app=~\"conversas-ai-api|conversas-ai-worker\"} |= \"FUNC_TIME\" | regexp `\\\\[FUNC_TIME\\\\]-(?P<function_name>\\\\w+)-(?P<execution_time>\\\\d+)ms` | unwrap execution_time | __error__=\"\" [15m]) by (function_name)", "legendFormat": "{{function_name}} - P95", "refId": "B"}, {"expr": "max_over_time({app=~\"conversas-ai-api|conversas-ai-worker\"} |= \"FUNC_TIME\" | regexp `\\\\[FUNC_TIME\\\\]-(?P<function_name>\\\\w+)-(?P<execution_time>\\\\d+)ms` | unwrap execution_time | __error__=\"\" [15m]) by (function_name)", "legendFormat": "{{function_name}} - Max", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "ms", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 3000}]}}}}, {"id": 5, "title": "Recent Function Executions", "type": "logs", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "datasource": {"type": "loki", "uid": "cen20vehcfpq8b"}, "targets": [{"expr": "{app=~\"conversas-ai-api|conversas-ai-worker\"} |= \"FUNC_TIME\" | regexp `\\\\[FUNC_TIME\\\\]-(?P<function_name>\\\\w+)-(?P<execution_time>\\\\d+)ms`", "refId": "A"}], "options": {"showTime": true, "showLabels": false, "showCommonLabels": true, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}}]}, "folderId": 0, "overwrite": true}