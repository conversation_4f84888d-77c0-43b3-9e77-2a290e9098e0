apiVersion: apps/v1
kind: Deployment
metadata:
  name: conversas-ai-api-qa
  namespace: conversas-ai
  labels:
    app: conversas-ai-api-qa
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0        # Garante que sempre há pods disponíveis
      maxSurge: 1             # Permite 1 pod extra durante deploy
  selector:
    matchLabels:
      app: conversas-ai-api-qa
  template:
    metadata:
      labels:
        app: conversas-ai-api-qa
    spec:
      imagePullSecrets:
        - name: gitlab-registry-secret
      containers:
      - name: api-qa
        image: registry.gitlab.com/plataformazw/ia/orion/api:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: conversas-ai-api-config-qa
        - secretRef:
            name: conversas-ai-api-secrets
        volumeMounts:
        - name: service-account-volume
          mountPath: /app/src/connections/conversas-ai.json
          subPath: conversas-ai.json
          readOnly: true         
        livenessProbe:
          httpGet:
            path: /health/
            port: 8080
          initialDelaySeconds: 60     # Mais tempo para inicialização
          periodSeconds: 30           # Menos frequente
          timeoutSeconds: 5
          failureThreshold: 3         # Permite algumas falhas
        readinessProbe:
          httpGet:
            path: /health/
            port: 8080
          initialDelaySeconds: 10     # Mais rápido para aceitar tráfego
          periodSeconds: 5            # Verifica mais frequentemente
          timeoutSeconds: 3
          failureThreshold: 2         # Falha mais rápido se não estiver ready
          successThreshold: 1
      volumes:
      - name: service-account-volume
        secret:
          secretName: conversas-ai-service-account
