apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: conversas-ai-api-hpa
  namespace: conversas-ai
  labels:
    app: conversas-ai
    component: api
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversas-ai-api
  minReplicas: 2
  maxReplicas: 8
  metrics:
  # CPU Utilization baseado nos limits (2000m)
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70  # 70% do limit de 2000m = 1400m
  # Memory Utilization baseado nos limits (4Gi)
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80  # 80% do limit de 4Gi = 3.2Gi
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600  # 10 min antes de reduzir (mais conservador)
      policies:
      - type: Percent
        value: 20  # Reduz máximo 20% das réplicas por vez
        periodSeconds: 120
      - type: Pods
        value: 1   # Reduz máximo 1 pod por vez
        periodSeconds: 120
      selectPolicy: Min  # Usa a política mais conservadora
    scaleUp:
      stabilizationWindowSeconds: 180   # 3 min antes de aumentar (mais tempo para estabilizar)
      policies:
      - type: Percent
        value: 50  # Aumenta máximo 50% das réplicas por vez (menos agressivo)
        periodSeconds: 60
      - type: Pods
        value: 1   # Aumenta máximo 1 pod por vez (mais controlado)
        periodSeconds: 60
      selectPolicy: Min  # Usa a política mais conservadora também para scale up
