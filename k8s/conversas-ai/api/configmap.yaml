apiVersion: v1
kind: ConfigMap
metadata:
  name: conversas-ai-api-config
  namespace: conversas-ai
  labels:
    app: conversas-ai-api
data:
  SERVER_PORT: "8080"
  REDIS_URL: "redis://redis-service.redis.svc.cluster.local:6379/0"
  FLASK_ENV: "production"
  WAITRESS_THREADS: "24"
  AUTH_ON: "true"
  GOOGLE_LOGIN_DOC: "true"
  DOMAIN: "https://api.conversas.ai"
  GLOBAL_RATE_LIMIT: "600/minute"
  RATE_LIMIT_ENABLED: "true"
  MODE: "api"
  ALLOWED_ORIGINS: "https://api.z-api.io, ************"
  RETRY_MESSAGE: "true"
  GCP_BIGQUERY_PROJECT_ID: "conversas-ai"
  GCP_BIGQUERY_DATASET: "production"
  JAEGER_HOST: "jaeger-service.jaeger.svc.cluster.local"
  JAEGER_PORT: "6831"
  SEPARATE_API_KEY: "true"