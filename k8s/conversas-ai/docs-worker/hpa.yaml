apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: conversas-ai-docs-worker-hpa
  namespace: conversas-ai
  labels:
    app: conversas-ai
    component: docs-worker
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversas-ai-docs-worker
  minReplicas: 1
  maxReplicas: 3
  metrics:
  # CPU Utilization - baseado no limit de 1000m do deployment
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80  # 80% do limit de 1000m = 800m
  # Memory Utilization - baseado no limit de 1Gi do deployment
  - type: Resource
    resource:
      name: memory
      target:
        type: AverageValue
        averageValue: 900Mi  # Valor absoluto de 900Mi (90% do limit de 1Gi)
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 900  # 15 min antes de reduzir (processamento de docs é esporádico)
      policies:
      - type: Percent
        value: 50  # Reduz máximo 50% das réplicas por vez
        periodSeconds: 180
      - type: Pods
        value: 1   # Reduz máximo 1 pod por vez
        periodSeconds: 180
      selectPolicy: Min  # Usa a política mais conservadora
    scaleUp:
      stabilizationWindowSeconds: 300  # 5 min antes de aumentar (aumentado para reduzir oscilações)
      policies:
      - type: Percent
        value: 100  # Aumenta máximo 100% das réplicas por vez
        periodSeconds: 60
      - type: Pods
        value: 1   # Reduz para 1 pod por vez para ser mais conservador
        periodSeconds: 60
      selectPolicy: Max  # Usa a política mais agressiva para scale up
