apiVersion: apps/v1
kind: Deployment
metadata:
  name: conversas-ai-docs-worker
  namespace: conversas-ai
  labels:
    app: conversas-ai-docs-worker
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
  selector:
    matchLabels:
      app: conversas-ai-docs-worker
  template:
    metadata:
      labels:
        app: conversas-ai-docs-worker
    spec:
      containers:
      - name: docs-worker
        image: registry.gitlab.com/plataformazw/ia/orion/docs_worker:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: conversas-ai-docs-worker-config
        livenessProbe:
          httpGet:
            path: /health_check
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health_check
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        resources:
          # requests:
          #   cpu: 500m        # Aumentado de 250m - baseado no uso médio observado
          #   memory: 512Mi    # Aumentado de 100Mi para 512Mi - baseado no uso real de ~406MB
          limits:
            cpu: 1000m       # Reduzido de 2024m para 1000m - mais realista
            memory: 1Gi      # Reduzido de 3G para 1Gi - mais adequado
      imagePullSecrets:
      - name: gitlab-registry-secret
