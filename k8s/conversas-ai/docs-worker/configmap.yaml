apiVersion: v1
kind: ConfigMap
metadata:
  name: conversas-ai-docs-worker-config
  namespace: conversas-ai
  labels:
    app: conversas-ai-docs-worker
data:
  TZ: "America/Sao_Paulo"
  SMTP_USERNAME: "<EMAIL>"
  SMTP_PASSWORD: "a99162af848595da80cdecf7ca9b1ca2-0920befd-9ec2e0a9"
  REDIS_URL: "redis://redis-service.redis.svc.cluster.local:6379/0"
  FLASK_ENV: production
  HEALTHCHECK_PORT: "8080"
  MODE: docs_worker
  QDRANT_HOST: "qdrant.qdrant.svc.cluster.local"
  QDRANT_PORT: "6333"
  JAEGER_HOST: "jaeger-service.jaeger.svc.cluster.local"
  JAEGER_PORT: "6831"