apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: conversas-ai-scheduler       
  namespace: conversas-ai
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversas-ai-scheduler
  pollingInterval: 5                  # (s) a cada quanto tempo o KEDA consulta o Redis
  cooldownPeriod: 300                 # (s) aguarda 5 min antes de reduzir réplicas
  minReplicaCount: 1
  maxReplicaCount: 5
  triggers:
  - type: redis
    metadata:
      address: redis-service.redis.svc.cluster.local:6379
      databaseIndex: "0"              # DB que você indicou na URL
      listName: scheduler_queue      # nome da key (zset)
      listLength: "60"               # alvo: máx. 100 itens pendentes por pod
      # Opcional: só escala acima de X itens totais (útil para evitar churn)
      # activationValue: "10"
