apiVersion: apps/v1
kind: Deployment
metadata:
  name: conversas-ai-scheduler
  namespace: conversas-ai
  labels:
    app: conversas-ai-scheduler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: conversas-ai-scheduler
  template:
    metadata:
      labels:
        app: conversas-ai-scheduler
    spec:
      imagePullSecrets:
        - name: gitlab-registry-secret
      containers:
      - name: scheduler
        image: registry.gitlab.com/plataformazw/ia/orion/scheduler:latest
        ports:
        - containerPort: 8081
        envFrom:
        - configMapRef:
            name: conversas-ai-scheduler-config
        - secretRef:
            name: conversas-ai-worker-secrets
        volumeMounts:
        - name: service-account-volume
          mountPath: /app/src/connections/conversas-ai.json
          subPath: conversas-ai.json
          readOnly: true
        resources:
          # requests:
          #   cpu: "600m"
          #   memory: "1024Mi"
          limits:
            cpu: "2048m"
            memory: "4G"
        livenessProbe:
          httpGet:
            path: /health_check
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health_check
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
      volumes:
      - name: service-account-volume
        secret:
          secretName: conversas-ai-service-account
