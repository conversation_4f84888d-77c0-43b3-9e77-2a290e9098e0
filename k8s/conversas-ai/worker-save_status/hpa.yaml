apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: conversas-ai-worker-updates-queue-scaler
  namespace: conversas-ai
  labels:
    app: conversas-ai
    component: worker-updates
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversas-ai-worker-updates
  pollingInterval: 15                 # Verifica a cada 15 segundos
  cooldownPeriod: 300                 # 5 minutos antes de reduzir
  minReplicaCount: 1                  # Mínimo de 2 réplicas
  maxReplicaCount: 12                 # Máximo de 12 réplicas (mantendo o mesmo do HPA original)
  advanced:
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 600  # 10 min antes de reduzir (workers demoram mais para processar)
          policies:
          - type: Percent
            value: 25  # Reduz máximo 25% das réplicas por vez
            periodSeconds: 120
          - type: Pods
            value: 1   # Reduz máximo 1 pod por vez
            periodSeconds: 120
          selectPolicy: Min  # Usa a política mais conservadora
        scaleUp:
          stabilizationWindowSeconds: 120  # 2 min antes de aumentar
          policies:
          - type: Percent
            value: 50  # Aumenta máximo 50% das réplicas por vez
            periodSeconds: 60
          - type: Pods
            value: 1   # Aumenta máximo 1 pod por vez (workers são mais pesados)
            periodSeconds: 60
          selectPolicy: Max  # Usa a política mais agressiva para scale up
  triggers:
  # Trigger baseado na fila Redis save_status_gymbot
  - type: redis
    metadata:
      address: redis-service.redis.svc.cluster.local:6379
      databaseIndex: "0"
      listName: save_status_gymbot      # Nome da fila no Redis
      listLength: "60"                # 1 instância para cada 60 mensagens
      activationListLength: "30"      # Ativa scaling quando tiver pelo menos 30 mensagens
  # Mantendo os triggers baseados em CPU e memória do HPA original
  - type: cpu
    metadata:
      type: AverageValue
      value: "1000m"
  - type: memory
    metadata:
      type: Utilization
      value: "75"
