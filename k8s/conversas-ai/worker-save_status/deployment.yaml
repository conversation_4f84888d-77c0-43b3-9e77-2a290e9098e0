apiVersion: apps/v1
kind: Deployment
metadata:
  name: conversas-ai-worker-save-status
  namespace: conversas-ai
  labels:
    app: conversas-ai-worker-save-status
spec:
  replicas: 1
  selector:
    matchLabels:
      app: conversas-ai-worker-save-status
  template:
    metadata:
      labels:
        app: conversas-ai-worker-save-status
    spec:
      imagePullSecrets:
        - name: gitlab-registry-secret
      containers:
      - name: worker-save-status
        image: registry.gitlab.com/plataformazw/ia/orion/worker:latest
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: conversas-ai-worker-save-status-config
        - secretRef:
            name: conversas-ai-worker-secrets
        volumeMounts:
        - name: service-account-volume
          mountPath: /app/src/connections/conversas-ai.json
          subPath: conversas-ai.json
          readOnly: true
        resources:
          requests:
            cpu: "300m"
            memory: "600Mi"
          limits:
            cpu: "1024m"
            memory: "2G"
        livenessProbe:
          httpGet:
            path: /health_check
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health_check
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
      volumes:
      - name: service-account-volume
        secret:
          secretName: conversas-ai-service-account
