apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: qdrant-ingress
  namespace: qdrant
  annotations:
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: qdrant-auth
    nginx.ingress.kubernetes.io/auth-realm: "Redis Insight Authentication"
spec:
  ingressClassName: nginx
  rules:
  - host: qdrant.conversas.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: qdrant
            port:
              number: 6333
