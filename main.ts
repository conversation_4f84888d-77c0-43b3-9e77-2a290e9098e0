import { Construct } from "constructs";
import { App, TerraformStack, TerraformOutput } from "cdktf";
import { GoogleProvider } from "./.gen/providers/google/provider";
import { ContainerCluster } from "./.gen/providers/google/container-cluster";
import { ContainerNodePool } from "./.gen/providers/google/container-node-pool";
import { ComputeNetwork } from "./.gen/providers/google/compute-network";
import { ComputeSubnetwork } from "./.gen/providers/google/compute-subnetwork";
import { ComputeFirewall } from "./.gen/providers/google/compute-firewall";
import { ComputeRouter } from "./.gen/providers/google/compute-router";
import { ComputeRouterNat } from "./.gen/providers/google/compute-router-nat";

export interface GkeClusterConfig {
  projectId: string;
  region: string;
  clusterName: string;
  kubernetesVersion: string;
  nodeCount: number;
  machineType: string;
  diskSizeGb: number;
  preemptibleNodes: boolean;
  minNodeCount: number;
  maxNodeCount: number;
  subnetIpCidrRange: string;
  clusterIpv4CidrBlock: string;
  servicesIpv4CidrBlock: string;
}

class GkeClusterStack extends TerraformStack {
  constructor(scope: Construct, id: string, config: GkeClusterConfig) {
    super(scope, id);

    new GoogleProvider(this, "google", {
      project: config.projectId,
      region: config.region,
    });

    const vpc = new ComputeNetwork(this, "vpc", {
      name: `${config.clusterName}-vpc`,
      autoCreateSubnetworks: false,
    });

    const subnet = new ComputeSubnetwork(this, "subnet", {
      name: `${config.clusterName}-subnet`,
      region: config.region,
      network: vpc.name,
      ipCidrRange: config.subnetIpCidrRange,
      privateIpGoogleAccess: true,
      // logConfig: {
      //   aggregationInterval: "INTERVAL_10_MIN",
      //   flowSampling: 0.5,
      //   metadata: "INCLUDE_ALL_METADATA",
      // },
    });

    new ComputeFirewall(this, "internal-firewall", {
      name: `${config.clusterName}-internal-firewall`,
      network: vpc.name,
      sourceRanges: [config.subnetIpCidrRange],
      allow: [
        {
          protocol: "icmp",
        },
        {
          protocol: "tcp",
        },
        {
          protocol: "udp",
        },
      ],
    });

    const router = new ComputeRouter(this, "router", {
      name: `${config.clusterName}-router`,
      region: config.region,
      network: vpc.name,
    });

    new ComputeRouterNat(this, "nat", {
      name: `${config.clusterName}-nat`,
      router: router.name,
      region: config.region,
      natIpAllocateOption: "AUTO_ONLY",
      sourceSubnetworkIpRangesToNat: "ALL_SUBNETWORKS_ALL_IP_RANGES",
      // logConfig: {
      //   enable: true,
      //   filter: "ERRORS_ONLY",
      // },
    });

    const cluster = new ContainerCluster(this, "gke-cluster", {
      name: config.clusterName,
      location: config.region,
      
      removeDefaultNodePool: true,
      initialNodeCount: 1,
      
      network: vpc.name,
      subnetwork: subnet.name,
      
      ipAllocationPolicy: {
        clusterIpv4CidrBlock: config.clusterIpv4CidrBlock,
        servicesIpv4CidrBlock: config.servicesIpv4CidrBlock,
      },
      
      workloadIdentityConfig: {
        workloadPool: `${config.projectId}.svc.id.goog`,
      },
      
      networkPolicy: {
        enabled: true,
      },
      
      minMasterVersion: config.kubernetesVersion,

      loggingService: "none",
      monitoringService: "none",
    });

    new ContainerNodePool(this, "primary-node-pool", {
      name: `${config.clusterName}-node-pool`,
      location: config.region,
      cluster: cluster.name,
      nodeCount: config.nodeCount,
      
      nodeConfig: {
        oauthScopes: [
          // "https://www.googleapis.com/auth/logging.write",
          // "https://www.googleapis.com/auth/monitoring",
          "https://www.googleapis.com/auth/devstorage.read_only",
          "https://www.googleapis.com/auth/compute",
        ],
        
        labels: {
          env: config.projectId,
        },
        
        machineType: config.machineType,
        diskSizeGb: config.diskSizeGb,
        diskType: "pd-standard",
        
        metadata: {
          "disable-legacy-endpoints": "true",
        },
        
        preemptible: config.preemptibleNodes,
        
        tags: ["gke-node", `${config.clusterName}-gke-node`],
      },
      
      autoscaling: {
        minNodeCount: config.minNodeCount,
        maxNodeCount: config.maxNodeCount,
      },
      
      upgradeSettings: {
        maxSurge: 1,
        maxUnavailable: 0,
      },
    });

    new TerraformOutput(this, "kubernetes_cluster_name", {
      value: cluster.name,
      description: "GKE Cluster Name",
    });

    new TerraformOutput(this, "kubernetes_cluster_host", {
      value: cluster.endpoint,
      description: "GKE Cluster Host",
      sensitive: true,
    });

    new TerraformOutput(this, "kubernetes_cluster_region", {
      value: config.region,
      description: "GKE Cluster Region",
    });

    new TerraformOutput(this, "vpc_network_name", {
      value: vpc.name,
      description: "The name of the VPC network",
    });

    new TerraformOutput(this, "subnet_name", {
      value: subnet.name,
      description: "The name of the subnet",
    });
  }
}

const defaultConfig: GkeClusterConfig = {
  projectId: "conversas-ai",
  region: "southamerica-east1",
  clusterName: "conversas-ai-cluster",
  kubernetesVersion: "1.32",
  nodeCount: 1,
  machineType: "e2-standard-2",
  diskSizeGb: 100,
  preemptibleNodes: false,
  minNodeCount: 1,
  maxNodeCount: 2,
  subnetIpCidrRange: "10.0.0.0/16",
  clusterIpv4CidrBlock: "*********/16",
  servicesIpv4CidrBlock: "*********/16",
};

let config: GkeClusterConfig;
try {
  const customConfig = require('./config').config;
  config = customConfig;
  console.log('Using custom configuration from config.ts');
} catch (e) {
  config = defaultConfig;
  console.log('Using default configuration (config.ts not found)');
}

const app = new App();
new GkeClusterStack(app, "cdktf-gke-cluster", config);
app.synth();
