
import { GkeClusterConfig } from './main';

export const config: GkeClusterConfig = {
  projectId: "your-gcp-project-id", // Replace with your GCP project ID
  region: "us-central1",
  clusterName: "my-gke-cluster",
  kubernetesVersion: "1.27",
  nodeCount: 3,
  machineType: "e2-standard-2",
  diskSizeGb: 100,
  preemptibleNodes: false,
  minNodeCount: 1,
  maxNodeCount: 5,
  subnetIpCidrRange: "10.0.0.0/16",
  clusterIpv4CidrBlock: "*********/16",
  servicesIpv4CidrBlock: "*********/16",
};
