{"//": {"metadata": {"backend": "local", "stackName": "cdktf-gke-cluster", "version": "0.20.11"}, "outputs": {"cdktf-gke-cluster": {"kubernetes_cluster_host": "kubernetes_cluster_host", "kubernetes_cluster_name": "kubernetes_cluster_name", "kubernetes_cluster_region": "kubernetes_cluster_region", "subnet_name": "subnet_name", "vpc_network_name": "vpc_network_name"}}}, "output": {"kubernetes_cluster_host": {"description": "GKE Cluster Host", "sensitive": true, "value": "${google_container_cluster.gke-cluster.endpoint}"}, "kubernetes_cluster_name": {"description": "GKE Cluster Name", "value": "${google_container_cluster.gke-cluster.name}"}, "kubernetes_cluster_region": {"description": "GKE Cluster Region", "value": "southamerica-east1"}, "subnet_name": {"description": "The name of the subnet", "value": "${google_compute_subnetwork.subnet.name}"}, "vpc_network_name": {"description": "The name of the VPC network", "value": "${google_compute_network.vpc.name}"}}, "provider": {"google": [{"project": "conversas-ai", "region": "southamerica-east1"}]}, "resource": {"google_compute_firewall": {"internal-firewall": {"//": {"metadata": {"path": "cdktf-gke-cluster/internal-firewall", "uniqueId": "internal-firewall"}}, "allow": [{"protocol": "icmp"}, {"protocol": "tcp"}, {"protocol": "udp"}], "name": "conversas-ai-cluster-internal-firewall", "network": "${google_compute_network.vpc.name}", "source_ranges": ["10.0.0.0/16"]}}, "google_compute_network": {"vpc": {"//": {"metadata": {"path": "cdktf-gke-cluster/vpc", "uniqueId": "vpc"}}, "auto_create_subnetworks": false, "name": "conversas-ai-cluster-vpc"}}, "google_compute_router": {"router": {"//": {"metadata": {"path": "cdktf-gke-cluster/router", "uniqueId": "router"}}, "name": "conversas-ai-cluster-router", "network": "${google_compute_network.vpc.name}", "region": "southamerica-east1"}}, "google_compute_router_nat": {"nat": {"//": {"metadata": {"path": "cdktf-gke-cluster/nat", "uniqueId": "nat"}}, "log_config": {"enable": true, "filter": "ERRORS_ONLY"}, "name": "conversas-ai-cluster-nat", "nat_ip_allocate_option": "AUTO_ONLY", "region": "southamerica-east1", "router": "${google_compute_router.router.name}", "source_subnetwork_ip_ranges_to_nat": "ALL_SUBNETWORKS_ALL_IP_RANGES"}}, "google_compute_subnetwork": {"subnet": {"//": {"metadata": {"path": "cdktf-gke-cluster/subnet", "uniqueId": "subnet"}}, "ip_cidr_range": "10.0.0.0/16", "log_config": {"aggregation_interval": "INTERVAL_10_MIN", "flow_sampling": 0.5, "metadata": "INCLUDE_ALL_METADATA"}, "name": "conversas-ai-cluster-subnet", "network": "${google_compute_network.vpc.name}", "private_ip_google_access": true, "region": "southamerica-east1"}}, "google_container_cluster": {"gke-cluster": {"//": {"metadata": {"path": "cdktf-gke-cluster/gke-cluster", "uniqueId": "gke-cluster"}}, "initial_node_count": 1, "ip_allocation_policy": {"cluster_ipv4_cidr_block": "*********/16", "services_ipv4_cidr_block": "*********/16"}, "location": "southamerica-east1", "min_master_version": "1.32", "name": "conversas-ai-cluster", "network": "${google_compute_network.vpc.name}", "network_policy": {"enabled": true}, "remove_default_node_pool": true, "subnetwork": "${google_compute_subnetwork.subnet.name}", "workload_identity_config": {"workload_pool": "conversas-ai.svc.id.goog"}}}, "google_container_node_pool": {"primary-node-pool": {"//": {"metadata": {"path": "cdktf-gke-cluster/primary-node-pool", "uniqueId": "primary-node-pool"}}, "autoscaling": {"max_node_count": 2, "min_node_count": 1}, "cluster": "${google_container_cluster.gke-cluster.name}", "location": "southamerica-east1", "name": "conversas-ai-cluster-node-pool", "node_config": {"disk_size_gb": 100, "disk_type": "pd-standard", "labels": {"env": "conversas-ai"}, "machine_type": "e2-standard-2", "metadata": {"disable-legacy-endpoints": "true"}, "oauth_scopes": ["https://www.googleapis.com/auth/logging.write", "https://www.googleapis.com/auth/monitoring", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/compute"], "preemptible": false, "tags": ["gke-node", "conversas-ai-cluster-gke-node"]}, "node_count": 1, "upgrade_settings": {"max_surge": 1, "max_unavailable": 0}}}}, "terraform": {"backend": {"local": {"path": "/home/<USER>/Projects/pacto-ai-infra/terraform.cdktf-gke-cluster.tfstate"}}, "required_providers": {"google": {"source": "hashicorp/google", "version": "4.85.0"}}}}