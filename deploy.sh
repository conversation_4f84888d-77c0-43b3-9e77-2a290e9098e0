

if ! command -v terraform &> /dev/null; then
    echo "Terraform is not installed. Please install it first."
    exit 1
fi

if ! command -v gcloud &> /dev/null; then
    echo "Google Cloud SDK is not installed. Please install it first."
    exit 1
fi

if ! gcloud auth application-default print-access-token &> /dev/null; then
    echo "You are not authenticated with Google Cloud. Please run 'gcloud auth application-default login' first."
    exit 1
fi

if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
fi

if [ ! -d ".gen" ]; then
    echo "Generating providers..."
    npm run get
fi

echo "Compiling TypeScript code..."
npm run compile

echo "Showing deployment plan..."
cdktf diff

read -p "Do you want to proceed with the deployment? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 1
fi

echo "Deploying infrastructure..."
cdktf deploy --auto-approve

echo "Deployment completed successfully!"
echo "To access your cluster, run:"
echo "gcloud container clusters get-credentials YOUR_CLUSTER_NAME --region YOUR_REGION --project YOUR_PROJECT_ID"
