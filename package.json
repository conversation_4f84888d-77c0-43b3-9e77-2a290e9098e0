{"name": "cdktf-gke-cluster", "version": "1.0.0", "main": "main.js", "types": "main.ts", "license": "MPL-2.0", "private": true, "scripts": {"get": "cdktf get", "deploy": "cdktf deploy", "plan": "cdktf plan", "destroy": "cdktf destroy", "output": "cdktf output", "build": "tsc", "diff": "cdktf diff", "synth": "cdktf synth", "compile": "tsc --pretty", "watch": "tsc -w", "test": "jest", "test:watch": "jest --watch", "upgrade": "npm i cdktf@latest cdktf-cli@latest", "upgrade:next": "npm i cdktf@next cdktf-cli@next"}, "engines": {"node": ">=18.0 <=20.x"}, "dependencies": {"cdktf": "^0.20.11", "cdktf-cli": "^0.20.11", "constructs": "^10.4.2"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.14.0", "jest": "^29.7.0", "ts-jest": "^29.3.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}