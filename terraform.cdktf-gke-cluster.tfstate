{"version": 4, "terraform_version": "1.11.4", "serial": 41, "lineage": "9c06d44d-60c4-e1bc-cb25-2854fc96f2da", "outputs": {"kubernetes_cluster_host": {"value": "***********", "type": "string", "sensitive": true}, "kubernetes_cluster_name": {"value": "conversas-ai-cluster", "type": "string"}, "kubernetes_cluster_region": {"value": "southamerica-east1", "type": "string"}, "subnet_name": {"value": "conversas-ai-cluster-subnet", "type": "string"}, "vpc_network_name": {"value": "conversas-ai-cluster-vpc", "type": "string"}}, "resources": [{"mode": "managed", "type": "google_compute_firewall", "name": "internal-firewall", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": [], "protocol": "icmp"}, {"ports": [], "protocol": "tcp"}, {"ports": [], "protocol": "udp"}], "creation_timestamp": "2025-04-16T08:49:36.630-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/conversas-ai/global/firewalls/conversas-ai-cluster-internal-firewall", "log_config": [], "name": "conversas-ai-cluster-internal-firewall", "network": "https://www.googleapis.com/compute/v1/projects/conversas-ai/global/networks/conversas-ai-cluster-vpc", "priority": 1000, "project": "conversas-ai", "self_link": "https://www.googleapis.com/compute/v1/projects/conversas-ai/global/firewalls/conversas-ai-cluster-internal-firewall", "source_ranges": ["10.0.0.0/16"], "source_service_accounts": null, "source_tags": null, "target_service_accounts": null, "target_tags": null, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc"]}]}, {"mode": "managed", "type": "google_compute_network", "name": "vpc", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/conversas-ai/global/networks/conversas-ai-cluster-vpc", "internal_ipv6_range": "", "mtu": 0, "name": "conversas-ai-cluster-vpc", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "project": "conversas-ai", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/conversas-ai/global/networks/conversas-ai-cluster-vpc", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "google_compute_router", "name": "router", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"bgp": [], "creation_timestamp": "2025-04-16T08:49:37.531-07:00", "description": "", "encrypted_interconnect_router": false, "id": "projects/conversas-ai/regions/southamerica-east1/routers/conversas-ai-cluster-router", "name": "conversas-ai-cluster-router", "network": "https://www.googleapis.com/compute/v1/projects/conversas-ai/global/networks/conversas-ai-cluster-vpc", "project": "conversas-ai", "region": "southamerica-east1", "self_link": "https://www.googleapis.com/compute/v1/projects/conversas-ai/regions/southamerica-east1/routers/conversas-ai-cluster-router", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc"]}]}, {"mode": "managed", "type": "google_compute_router_nat", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"drain_nat_ips": null, "enable_dynamic_port_allocation": false, "enable_endpoint_independent_mapping": true, "icmp_idle_timeout_sec": 30, "id": "conversas-ai/southamerica-east1/conversas-ai-cluster-router/conversas-ai-cluster-nat", "log_config": [{"enable": true, "filter": "ERRORS_ONLY"}], "max_ports_per_vm": 0, "min_ports_per_vm": 0, "name": "conversas-ai-cluster-nat", "nat_ip_allocate_option": "AUTO_ONLY", "nat_ips": null, "project": "conversas-ai", "region": "southamerica-east1", "router": "conversas-ai-cluster-router", "rules": [], "source_subnetwork_ip_ranges_to_nat": "ALL_SUBNETWORKS_ALL_IP_RANGES", "subnetwork": [], "tcp_established_idle_timeout_sec": 1200, "tcp_time_wait_timeout_sec": 120, "tcp_transitory_idle_timeout_sec": 30, "timeouts": null, "udp_idle_timeout_sec": 30}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc", "google_compute_router.router"]}]}, {"mode": "managed", "type": "google_compute_subnetwork", "name": "subnet", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"creation_timestamp": "2025-04-16T08:49:38.756-07:00", "description": "", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "********", "id": "projects/conversas-ai/regions/southamerica-east1/subnetworks/conversas-ai-cluster-subnet", "internal_ipv6_prefix": "", "ip_cidr_range": "10.0.0.0/16", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [{"aggregation_interval": "INTERVAL_10_MIN", "filter_expr": "true", "flow_sampling": 0.5, "metadata": "INCLUDE_ALL_METADATA", "metadata_fields": null}], "name": "conversas-ai-cluster-subnet", "network": "https://www.googleapis.com/compute/v1/projects/conversas-ai/global/networks/conversas-ai-cluster-vpc", "private_ip_google_access": true, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "conversas-ai", "purpose": "PRIVATE", "region": "southamerica-east1", "role": "", "secondary_ip_range": [], "self_link": "https://www.googleapis.com/compute/v1/projects/conversas-ai/regions/southamerica-east1/subnetworks/conversas-ai-cluster-subnet", "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc"]}]}, {"mode": "managed", "type": "google_container_cluster", "name": "gke-cluster", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"addons_config": [{"cloudrun_config": [], "config_connector_config": [], "dns_cache_config": [], "gce_persistent_disk_csi_driver_config": [{"enabled": true}], "gcp_filestore_csi_driver_config": [], "gcs_fuse_csi_driver_config": [], "gke_backup_agent_config": [], "horizontal_pod_autoscaling": [], "http_load_balancing": [], "network_policy_config": [{"disabled": false}]}], "allow_net_admin": null, "authenticator_groups_config": [], "binary_authorization": [{"enabled": false, "evaluation_mode": ""}], "cluster_autoscaling": [{"auto_provisioning_defaults": [], "enabled": false, "resource_limits": []}], "cluster_ipv4_cidr": "*********/16", "confidential_nodes": [], "cost_management_config": [], "database_encryption": [{"key_name": "", "state": "DECRYPTED"}], "datapath_provider": "", "default_max_pods_per_node": 110, "default_snat_status": [{"disabled": false}], "description": "", "dns_config": [], "enable_autopilot": false, "enable_binary_authorization": false, "enable_intranode_visibility": false, "enable_k8s_beta_apis": [], "enable_kubernetes_alpha": false, "enable_l4_ilb_subsetting": false, "enable_legacy_abac": false, "enable_shielded_nodes": true, "enable_tpu": false, "endpoint": "***********", "gateway_api_config": [], "id": "projects/conversas-ai/locations/southamerica-east1/clusters/conversas-ai-cluster", "initial_node_count": 1, "ip_allocation_policy": [{"additional_pod_ranges_config": [], "cluster_ipv4_cidr_block": "*********/16", "cluster_secondary_range_name": "gke-conversas-ai-cluster-pods-81f48c80", "pod_cidr_overprovision_config": [{"disabled": false}], "services_ipv4_cidr_block": "*********/16", "services_secondary_range_name": "gke-conversas-ai-cluster-services-81f48c80", "stack_type": "IPV4"}], "label_fingerprint": "a9dc16a7", "location": "southamerica-east1", "logging_config": [{"enable_components": ["SYSTEM_COMPONENTS", "WORKLOADS"]}], "logging_service": "logging.googleapis.com/kubernetes", "maintenance_policy": [], "master_auth": [{"client_certificate": "", "client_certificate_config": [{"issue_client_certificate": false}], "client_key": "", "cluster_ca_certificate": "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"}], "master_authorized_networks_config": [], "master_version": "1.32.2-gke.1182001", "mesh_certificates": [], "min_master_version": "1.32", "monitoring_config": [{"advanced_datapath_observability_config": [{"enable_metrics": false, "relay_mode": ""}], "enable_components": ["SYSTEM_COMPONENTS", "HPA", "POD", "DAEMONSET", "DEPLOYMENT", "STATEFULSET", "JOBSET", "STORAGE", "CADVISOR", "KUBELET", "DCGM"], "managed_prometheus": [{"enabled": true}]}], "monitoring_service": "monitoring.googleapis.com/kubernetes", "name": "conversas-ai-cluster", "network": "projects/conversas-ai/global/networks/conversas-ai-cluster-vpc", "network_policy": [{"enabled": true, "provider": ""}], "networking_mode": "VPC_NATIVE", "node_config": [], "node_locations": ["southamerica-east1-a", "southamerica-east1-b", "southamerica-east1-c"], "node_pool": [], "node_pool_auto_config": [{"network_tags": null}], "node_pool_defaults": [{"node_config_defaults": [{"logging_variant": "DEFAULT"}]}], "node_version": "1.32.2-gke.1182001", "notification_config": [{"pubsub": [{"enabled": false, "filter": [], "topic": ""}]}], "operation": null, "private_cluster_config": [{"enable_private_endpoint": false, "enable_private_nodes": false, "master_global_access_config": [{"enabled": false}], "master_ipv4_cidr_block": "", "peering_name": "", "private_endpoint": "********", "private_endpoint_subnetwork": "", "public_endpoint": "***********"}], "private_ipv6_google_access": "", "project": "conversas-ai", "release_channel": [{"channel": "STABLE"}], "remove_default_node_pool": true, "resource_labels": null, "resource_usage_export_config": [], "security_posture_config": [{"mode": "BASIC", "vulnerability_mode": "VULNERABILITY_MODE_UNSPECIFIED"}], "self_link": "https://container.googleapis.com/v1/projects/conversas-ai/locations/southamerica-east1/clusters/conversas-ai-cluster", "service_external_ips_config": [{"enabled": false}], "services_ipv4_cidr": "*********/16", "subnetwork": "projects/conversas-ai/regions/southamerica-east1/subnetworks/conversas-ai-cluster-subnet", "timeouts": null, "tpu_ipv4_cidr_block": "", "vertical_pod_autoscaling": [], "workload_identity_config": [{"workload_pool": "conversas-ai.svc.id.goog"}]}, "sensitive_attributes": [[{"type": "get_attr", "value": "master_auth"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "client_key"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoyNDAwMDAwMDAwMDAwLCJkZWxldGUiOjI0MDAwMDAwMDAwMDAsInJlYWQiOjI0MDAwMDAwMDAwMDAsInVwZGF0ZSI6MzYwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["google_compute_network.vpc", "google_compute_subnetwork.subnet"]}]}, {"mode": "managed", "type": "google_container_node_pool", "name": "primary-node-pool", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 2, "min_node_count": 1, "total_max_node_count": 0, "total_min_node_count": 0}], "cluster": "conversas-ai-cluster", "id": "projects/conversas-ai/locations/southamerica-east1/clusters/conversas-ai-cluster/nodePools/conversas-ai-cluster-node-pool", "initial_node_count": 1, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/conversas-ai/zones/southamerica-east1-a/instanceGroupManagers/gke-conversas-ai-clu-conversas-ai-clu-221bf0dd-grp", "https://www.googleapis.com/compute/v1/projects/conversas-ai/zones/southamerica-east1-c/instanceGroupManagers/gke-conversas-ai-clu-conversas-ai-clu-ec6a4c52-grp", "https://www.googleapis.com/compute/v1/projects/conversas-ai/zones/southamerica-east1-b/instanceGroupManagers/gke-conversas-ai-clu-conversas-ai-clu-b10a37f1-grp"], "location": "southamerica-east1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/conversas-ai/zones/southamerica-east1-a/instanceGroups/gke-conversas-ai-clu-conversas-ai-clu-221bf0dd-grp", "https://www.googleapis.com/compute/v1/projects/conversas-ai/zones/southamerica-east1-c/instanceGroups/gke-conversas-ai-clu-conversas-ai-clu-ec6a4c52-grp", "https://www.googleapis.com/compute/v1/projects/conversas-ai/zones/southamerica-east1-b/instanceGroups/gke-conversas-ai-clu-conversas-ai-clu-b10a37f1-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "conversas-ai-cluster-node-pool", "name_prefix": "", "network_config": [{"create_pod_range": false, "enable_private_nodes": false, "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "*********/16", "pod_range": "gke-conversas-ai-cluster-pods-81f48c80"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "disk_size_gb": 100, "disk_type": "pd-standard", "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [{"cpu_cfs_quota": false, "cpu_cfs_quota_period": "", "cpu_manager_policy": "", "pod_pids_limit": 0}], "labels": {"env": "conversas-ai"}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-2", "metadata": {"disable-legacy-endpoints": "true"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/compute", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/logging.write", "https://www.googleapis.com/auth/monitoring"], "preemptible": false, "reservation_affinity": [], "resource_labels": {"goog-gke-node-pool-provisioning-model": "on-demand"}, "service_account": "default", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": ["gke-node", "conversas-ai-cluster-gke-node"], "taint": [], "workload_metadata_config": [{"mode": "GKE_METADATA"}]}], "node_count": 1, "node_locations": ["southamerica-east1-a", "southamerica-east1-b", "southamerica-east1-c"], "operation": null, "placement_policy": [], "project": "conversas-ai", "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.32.2-gke.1182001"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc", "google_compute_subnetwork.subnet", "google_container_cluster.gke-cluster"]}]}], "check_results": null}